#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python环境检查和修复脚本
"""

import sys
import os
import subprocess
import platform

def check_python_version():
    """检查Python版本"""
    print("=" * 50)
    print("🔍 Python环境检查")
    print("=" * 50)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"架构: {platform.machine()}")
    
    if sys.version_info < (3, 6):
        print("⚠️ 警告: Python版本过低，建议使用Python 3.6+")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def check_required_modules():
    """检查必需的模块"""
    print("\n🔍 检查必需模块...")
    
    required_modules = [
        ('http.server', '内置HTTP服务器'),
        ('json', 'JSON处理'),
        ('datetime', '日期时间处理'),
        ('logging', '日志记录'),
        ('urllib.request', 'URL请求'),
        ('urllib.parse', 'URL解析')
    ]
    
    optional_modules = [
        ('requests', 'HTTP请求库'),
        ('urllib3', 'HTTP客户端')
    ]
    
    all_good = True
    
    # 检查必需模块
    for module_name, description in required_modules:
        try:
            __import__(module_name)
            print(f"✅ {module_name} - {description}")
        except ImportError:
            print(f"❌ {module_name} - {description} (缺失)")
            all_good = False
    
    # 检查可选模块
    print("\n🔍 检查可选模块...")
    for module_name, description in optional_modules:
        try:
            __import__(module_name)
            print(f"✅ {module_name} - {description}")
        except ImportError:
            print(f"⚠️ {module_name} - {description} (未安装，将使用备用方案)")
    
    return all_good

def check_network():
    """检查网络连接"""
    print("\n🔍 检查网络连接...")
    
    try:
        import urllib.request
        
        # 测试连接到东方财富API
        test_url = "http://push2.eastmoney.com/api/qt/stock/get?secid=90.BK0447&fields=f43"
        
        req = urllib.request.Request(test_url)
        req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        
        response = urllib.request.urlopen(req, timeout=10)
        if response.getcode() == 200:
            print("✅ 网络连接正常，可以访问东方财富API")
            return True
        else:
            print(f"⚠️ 网络连接异常，状态码: {response.getcode()}")
            return False
            
    except Exception as e:
        print(f"❌ 网络连接失败: {e}")
        return False

def check_port_availability():
    """检查端口可用性"""
    print("\n🔍 检查端口8888可用性...")
    
    import socket
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', 8888))
        sock.close()
        
        if result == 0:
            print("⚠️ 端口8888已被占用")
            return False
        else:
            print("✅ 端口8888可用")
            return True
            
    except Exception as e:
        print(f"❌ 端口检查失败: {e}")
        return False

def install_requests():
    """尝试安装requests库"""
    print("\n🔧 尝试安装requests库...")
    
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'requests'])
        print("✅ requests库安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ requests库安装失败")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--user', 'requests'])
            print("✅ requests库安装成功 (用户模式)")
            return True
        except subprocess.CalledProcessError:
            print("❌ requests库安装失败 (用户模式)")
            return False
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def test_server():
    """测试服务器启动"""
    print("\n🔍 测试HTTP服务器...")
    
    try:
        from http.server import HTTPServer, BaseHTTPRequestHandler
        
        class TestHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                self.send_response(200)
                self.send_header('Content-type', 'text/html')
                self.end_headers()
                self.wfile.write(b'<h1>Test Server OK</h1>')
            
            def log_message(self, format, *args):
                pass  # 禁用日志输出
        
        # 尝试启动测试服务器
        server = HTTPServer(('localhost', 8889), TestHandler)
        print("✅ HTTP服务器测试成功")
        server.server_close()
        return True
        
    except Exception as e:
        print(f"❌ HTTP服务器测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Python环境诊断和修复工具")
    print("用于股票推荐系统环境检查")
    
    # 检查Python版本
    python_ok = check_python_version()
    
    # 检查必需模块
    modules_ok = check_required_modules()
    
    # 检查网络连接
    network_ok = check_network()
    
    # 检查端口可用性
    port_ok = check_port_availability()
    
    # 测试服务器
    server_ok = test_server()
    
    print("\n" + "=" * 50)
    print("📊 诊断结果汇总")
    print("=" * 50)
    
    print(f"Python版本: {'✅' if python_ok else '❌'}")
    print(f"必需模块: {'✅' if modules_ok else '❌'}")
    print(f"网络连接: {'✅' if network_ok else '❌'}")
    print(f"端口8888: {'✅' if port_ok else '❌'}")
    print(f"HTTP服务器: {'✅' if server_ok else '❌'}")
    
    if all([python_ok, modules_ok, server_ok]):
        print("\n🎉 环境检查通过！可以启动股票推荐服务器")
        print("\n🚀 启动命令:")
        print("   python realtime_stock_server.py")
        print("\n🌐 访问地址:")
        print("   http://localhost:8888")
        
        # 询问是否立即启动
        try:
            choice = input("\n是否立即启动服务器? (y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                print("\n🚀 正在启动服务器...")
                import realtime_stock_server
                realtime_stock_server.main()
        except KeyboardInterrupt:
            print("\n👋 用户取消")
        except Exception as e:
            print(f"\n❌ 启动失败: {e}")
    else:
        print("\n⚠️ 环境存在问题，需要修复")
        
        if not network_ok:
            print("\n🔧 网络问题解决建议:")
            print("   1. 检查网络连接")
            print("   2. 检查防火墙设置")
            print("   3. 尝试使用VPN或代理")
        
        if not port_ok:
            print("\n🔧 端口问题解决建议:")
            print("   1. 关闭占用端口8888的程序")
            print("   2. 或修改服务器端口号")
        
        # 尝试安装requests
        try:
            import requests
        except ImportError:
            choice = input("\n是否尝试安装requests库? (y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                install_requests()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按Enter键退出...")
