#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整版API数据
"""

import requests
import json

try:
    print("正在测试完整版API (端口7777)...")
    r = requests.get('http://localhost:7777/api/data', timeout=10)
    print(f"HTTP状态码: {r.status_code}")
    
    data = r.json()
    
    print("API状态:", data['status'])
    print("服务器时间:", data.get('server_time', '未知'))
    
    # 测试市场数据（包含港股）
    market = data['data']['market']
    print(f"\n📊 完整市场数据 (共{len(market)}个指数):")
    for code, info in market.items():
        change_symbol = "🔴" if info['change'] >= 0 else "🟢"
        market_type = "港股" if code.startswith('hk_') else "A股"
        print(f"{change_symbol} [{market_type}] {info['name']}: {info['close']:.2f}点 ({info['change_pct']:+.2f}%) - 数据源: {info.get('source', '未知')}")
    
    # 测试板块数据
    if 'sectors' in data['data']:
        sectors = data['data']['sectors']
        print(f"\n🔥 智能板块推荐 (共{len(sectors)}个板块):")
        for i, sector in enumerate(sectors[:5], 1):  # 显示前5个
            print(f"{i}. 📈 {sector['name']}: {sector['score']}分 ({sector['recommendation_level']}) - 今日{sector['current_change_pct']:+.2f}%")
            print(f"   前景评级: {sector['industry_outlook']} | 风险等级: {sector['risk_level']} | 资讯情绪: {sector['news_sentiment']}%")
            print(f"   历史业绩: 1月{sector['historical_performance']['1m']:+.1f}% | 3月{sector['historical_performance']['3m']:+.1f}% | 年内{sector['historical_performance']['ytd']:+.1f}%")
            print(f"   推荐理由: {sector['reason'][:80]}...")
            print()
    else:
        print("\n❌ 未找到板块数据")
    
    print(f"✅ 完整版API测试成功!")
    print(f"🌐 请访问: http://localhost:7777")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
