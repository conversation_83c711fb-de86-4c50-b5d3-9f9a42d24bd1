import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """配置类"""
    
    # Tushare API配置
    TUSHARE_TOKEN = os.getenv('TUSHARE_TOKEN', 'your_tushare_token_here')
    
    # 数据库配置
    DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///stock_data.db')
    
    # Redis配置（用于缓存）
    REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    
    # Flask配置
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here')
    DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
    
    # 数据更新配置
    DATA_UPDATE_INTERVAL = int(os.getenv('DATA_UPDATE_INTERVAL', '3600'))  # 秒
    
    # 分析参数配置
    ANALYSIS_PARAMS = {
        'ma_periods': [5, 10, 20, 60],  # 移动平均线周期
        'rsi_period': 14,  # RSI周期
        'macd_params': (12, 26, 9),  # MACD参数
        'bollinger_period': 20,  # 布林带周期
        'volume_ma_period': 20,  # 成交量移动平均周期
    }
    
    # 荐股策略配置
    RECOMMENDATION_PARAMS = {
        'min_market_cap': 50,  # 最小市值（亿元）
        'max_pe_ratio': 50,  # 最大市盈率
        'min_roe': 0.08,  # 最小净资产收益率
        'min_revenue_growth': 0.1,  # 最小营收增长率
        'sector_rotation_period': 30,  # 板块轮动分析周期（天）
    }
    
    # 风险控制配置
    RISK_PARAMS = {
        'max_position_ratio': 0.1,  # 单只股票最大仓位比例
        'stop_loss_ratio': 0.08,  # 止损比例
        'take_profit_ratio': 0.15,  # 止盈比例
        'max_drawdown': 0.2,  # 最大回撤
    }
    
    # 数据源配置
    DATA_SOURCES = {
        'primary': 'tushare',
        'backup': 'yfinance',
        'news': 'tushare_news'
    }
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'stock_picker.log')
