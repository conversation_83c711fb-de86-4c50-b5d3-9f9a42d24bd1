#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新API数据
"""

import requests
import json

try:
    print("正在测试新端口8888的API...")
    r = requests.get('http://localhost:8888/api/data', timeout=10)
    print(f"HTTP状态码: {r.status_code}")
    
    data = r.json()
    
    print("API状态:", data['status'])
    print("服务器时间:", data.get('server_time', '未知'))
    
    market = data['data']['market']
    print(f"\n当前市场数据 (共{len(market)}个指数):")
    for code, info in market.items():
        change_symbol = "🔴" if info['change'] >= 0 else "🟢"
        print(f"{change_symbol} {info['name']}: {info['close']:.2f}点 ({info['change_pct']:+.2f}%) - 数据源: {info.get('source', '未知')}")
    
    print(f"\n✅ 新端口API测试成功!")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
