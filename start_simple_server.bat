@echo off
title 股票推荐系统服务器启动器
color 0A

echo ========================================
echo 🚀 股票推荐系统服务器启动器
echo ========================================
echo.

cd /d "C:\Users\<USER>\Desktop\stock_app"
echo 📁 当前目录: %CD%
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 找到Python环境
    set PYTHON_CMD=python
    goto :start_server
)

py --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 找到Python环境 (py命令)
    set PYTHON_CMD=py
    goto :start_server
)

echo ❌ Python环境未找到
echo.
echo 🔧 解决方案:
echo 1. 直接访问HTML文件 (推荐)
echo 2. 安装Python后重试
echo.

:ask_choice
echo 请选择操作:
echo [1] 打开实时股票应用 (HTML文件)
echo [2] 打开准确数据应用 (HTML文件)
echo [3] 退出
echo.
set /p choice="请输入选择 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 🌐 正在打开实时股票应用...
    start "" "realtime_stock_app.html"
    echo ✅ 已在浏览器中打开实时股票应用
    goto :end
)

if "%choice%"=="2" (
    echo.
    echo 🌐 正在打开准确数据应用...
    start "" "final_accurate_stock_app.html"
    echo ✅ 已在浏览器中打开准确数据应用
    goto :end
)

if "%choice%"=="3" (
    goto :end
)

echo ❌ 无效选择，请重新输入
goto :ask_choice

:start_server
echo.
echo 🔍 检查端口8888...
netstat -ano | findstr :8888 >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️ 端口8888已被占用
    echo 正在尝试终止占用进程...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8888') do (
        taskkill /PID %%a /F >nul 2>&1
    )
    timeout /t 2 >nul
)

echo ✅ 端口8888可用
echo.

echo 🚀 启动HTTP服务器...
echo 📍 端口: 8888
echo 🌐 访问地址: http://localhost:8888
echo.
echo 可用页面:
echo   http://localhost:8888/realtime_stock_app.html (实时数据)
echo   http://localhost:8888/final_accurate_stock_app.html (准确数据)
echo.
echo 按 Ctrl+C 停止服务器
echo ========================================
echo.

%PYTHON_CMD% -m http.server 8888

:end
echo.
echo 👋 感谢使用股票推荐系统
pause
