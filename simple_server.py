from http.server import HTTPServer, BaseHTTPRequestHandler
import json
from datetime import datetime

class StockHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        print(f"Request: {self.path}")
        self.send_response(200)
        
        if self.path == '/':
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            
            html = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Stock Server - localhost:8888</title>
    <style>
        body { font-family: Arial; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .success { background: #27ae60; color: white; padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 30px; }
        .sector { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 8px; }
        .highlight { border: 2px solid #f39c12; background: #fff9e6; }
        .up { color: #e74c3c; }
        .down { color: #27ae60; }
        button { background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Stock Recommendation System</h1>
        <div class="success">
            ✅ http://localhost:8888 is working!<br>
            📊 Semiconductor: Index 2831.81, Change -1.15%
        </div>
        <div id="data">Loading...</div>
        <button onclick="loadData()">🔄 Refresh Data</button>
        <div style="margin-top: 15px;">Last Update: <span id="time">--:--:--</span></div>
    </div>
    
    <script>
        function loadData() {
            fetch('/api')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('data');
                    container.innerHTML = '';
                    
                    data.sectors.forEach(sector => {
                        const div = document.createElement('div');
                        div.className = 'sector' + (sector.name === '半导体' ? ' highlight' : '');
                        div.innerHTML = `
                            <strong>${sector.name}</strong><br>
                            Index: ${sector.index_value}<br>
                            Change: <span class="${sector.current_change_pct >= 0 ? 'up' : 'down'}">${sector.current_change_pct >= 0 ? '+' : ''}${sector.current_change_pct}%</span>
                        `;
                        container.appendChild(div);
                    });
                    
                    document.getElementById('time').textContent = data.update_time;
                    console.log('Data loaded:', data);
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('data').innerHTML = '<div style="color: red;">Error loading data</div>';
                });
        }
        
        window.onload = loadData;
    </script>
</body>
</html>"""
            
            self.wfile.write(html.encode('utf-8'))
            
        elif self.path == '/api':
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.end_headers()
            
            # Stock data with accurate semiconductor data
            data = {
                'status': 'success',
                'sectors': [
                    {'name': '半导体', 'current_change_pct': -1.15, 'index_value': 2831.81},
                    {'name': '人工智能', 'current_change_pct': 1.25, 'index_value': 1245.67},
                    {'name': '新能源汽车', 'current_change_pct': -0.68, 'index_value': 2156.43},
                    {'name': '医疗器械', 'current_change_pct': 0.32, 'index_value': 1876.92},
                    {'name': '白酒食品', 'current_change_pct': 0.78, 'index_value': 3421.56},
                    {'name': '银行金融', 'current_change_pct': 0.25, 'index_value': 1987.34}
                ],
                'update_time': datetime.now().strftime('%H:%M:%S')
            }
            
            print(f"Returning data: Semiconductor {data['sectors'][0]['index_value']} ({data['sectors'][0]['current_change_pct']}%)")
            
            self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
        else:
            self.send_response(404)
            self.end_headers()
    
    def log_message(self, format, *args):
        pass  # Suppress default logging

if __name__ == '__main__':
    PORT = 8888
    
    print("=" * 50)
    print("🚀 Simple Stock Server")
    print(f"📍 Port: {PORT}")
    print(f"🌐 URL: http://localhost:{PORT}")
    print("📊 Semiconductor: 2831.81 (-1.15%)")
    print("=" * 50)
    
    try:
        server = HTTPServer(('localhost', PORT), StockHandler)
        print(f"✅ Server started successfully!")
        print("Press Ctrl+C to stop")
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"❌ Error: {e}")
