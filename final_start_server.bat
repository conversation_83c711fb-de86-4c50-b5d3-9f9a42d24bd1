@echo off
title 股票推荐系统 - 最终解决方案
color 0A

echo ========================================
echo 🚀 股票推荐系统 - 最终解决方案
echo ========================================
echo.

echo 📁 获取脚本目录...
set "SCRIPT_DIR=%~dp0"
echo 脚本目录: %SCRIPT_DIR%

echo.
echo 📂 切换到脚本目录...
cd /d "%SCRIPT_DIR%"
echo 当前目录: %CD%

echo.
echo 🔍 检查Python环境...
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 找到Python环境
    set PYTHON_CMD=python
    goto :check_files
)

py --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 找到Python环境 (py命令)
    set PYTHON_CMD=py
    goto :check_files
)

echo ❌ Python环境未找到
goto :no_python

:check_files
echo.
echo 🔍 检查必要文件...
if exist "standalone_server.py" (
    echo ✅ 找到 standalone_server.py
) else (
    echo ❌ 未找到 standalone_server.py
    goto :no_files
)

if exist "realtime_stock_app.html" (
    echo ✅ 找到 realtime_stock_app.html
) else (
    echo ⚠️ 未找到 realtime_stock_app.html (可选)
)

echo.
echo 🔍 检查端口8888...
netstat -ano | findstr :8888 >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️ 端口8888已被占用，尝试终止占用进程...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8888') do (
        taskkill /PID %%a /F >nul 2>&1
    )
    timeout /t 2 >nul
    echo ✅ 端口清理完成
) else (
    echo ✅ 端口8888可用
)

echo.
echo 🚀 启动独立服务器...
echo 📍 端口: 8888
echo 🌐 访问地址: http://localhost:8888
echo.
echo 📊 功能特色:
echo   ✅ 解决Python环境问题
echo   ✅ 自包含所有文件
echo   ✅ 半导体板块数据准确: 指数2831.81, 涨跌幅-1.15%%
echo   ✅ 实时数据更新
echo.
echo 按 Ctrl+C 停止服务器
echo ========================================
echo.

%PYTHON_CMD% "%SCRIPT_DIR%standalone_server.py"
goto :end

:no_python
echo.
echo 🔧 Python环境解决方案:
echo 1. 安装Python 3.x版本
echo 2. 确保Python已添加到系统PATH
echo 3. 重启命令提示符
echo.
echo 🌐 备用方案 - 直接打开HTML文件:
echo.
set /p choice="是否打开HTML文件? (y/n): "
if /i "%choice%"=="y" (
    if exist "realtime_stock_app.html" (
        start "" "realtime_stock_app.html"
        echo ✅ 已在浏览器中打开实时股票应用
    ) else if exist "final_accurate_stock_app.html" (
        start "" "final_accurate_stock_app.html"
        echo ✅ 已在浏览器中打开准确数据应用
    ) else (
        echo ❌ 未找到HTML文件
    )
)
goto :end

:no_files
echo.
echo ❌ 缺少必要文件
echo 请确保以下文件存在:
echo   - standalone_server.py
echo   - realtime_stock_app.html (可选)
goto :end

:end
echo.
echo 👋 感谢使用股票推荐系统
pause
