<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时荐股小程序 - 基于真实数据</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #007bff;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 20px auto;
            max-width: 1200px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }
        
        .real-time-indicator {
            position: absolute;
            top: 15px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            margin-right: 5px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .content {
            padding: 20px;
        }
        
        .section-title {
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--primary-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .last-update {
            font-size: 0.8rem;
            color: #666;
            font-weight: normal;
        }
        
        .market-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .market-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .market-card:hover {
            transform: translateY(-3px);
        }
        
        .market-card.positive {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .market-card.negative {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        }
        
        .market-price {
            font-size: 1.8rem;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .recommendation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .recommendation-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid var(--primary-color);
        }
        
        .recommendation-card:hover {
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        
        .stock-card {
            border-left-color: var(--warning-color);
        }
        
        .sector-card {
            border-left-color: var(--success-color);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
        }
        
        .score-badge {
            background: var(--primary-color);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }
        
        .score-excellent { background: var(--success-color); }
        .score-good { background: var(--info-color); }
        .score-fair { background: var(--warning-color); }
        
        .indicator-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
        }
        
        .positive { color: var(--success-color); font-weight: bold; }
        .negative { color: var(--danger-color); font-weight: bold; }
        
        .reason {
            margin-top: 10px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
            font-size: 0.9rem;
            color: #666;
        }
        
        .news-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid var(--info-color);
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .auto-refresh {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 10px 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .auto-refresh:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .auto-refresh.active {
            background: var(--success-color);
        }
        
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 10px;
            }
            .content {
                padding: 15px;
            }
            .market-grid,
            .recommendation-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <div class="real-time-indicator">
                <div class="status-dot"></div>
                <span>实时更新</span>
            </div>
            <h1><i class="fas fa-chart-line"></i> 实时荐股小程序</h1>
            <p>基于真实数据的智能股票推荐系统</p>
        </div>
        
        <div class="content">
            <!-- 市场概览 -->
            <section>
                <h2 class="section-title">
                    <span><i class="fas fa-globe"></i> 市场概览</span>
                    <span class="last-update" id="market-update-time">更新中...</span>
                </h2>
                <div class="market-grid" id="market-data">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i> 正在获取实时数据...
                    </div>
                </div>
            </section>
            
            <!-- 股票推荐 -->
            <section>
                <h2 class="section-title">
                    <span><i class="fas fa-star"></i> 实时股票推荐</span>
                    <span class="last-update" id="stock-update-time">更新中...</span>
                </h2>
                <div class="recommendation-grid" id="stock-recommendations">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i> 正在分析股票数据...
                    </div>
                </div>
            </section>
            
            <!-- 板块推荐 -->
            <section>
                <h2 class="section-title">
                    <span><i class="fas fa-layer-group"></i> 热门板块</span>
                    <span class="last-update" id="sector-update-time">更新中...</span>
                </h2>
                <div class="recommendation-grid" id="sector-recommendations">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i> 正在分析板块数据...
                    </div>
                </div>
            </section>
            
            <!-- 市场资讯 -->
            <section>
                <h2 class="section-title">
                    <span><i class="fas fa-newspaper"></i> 市场资讯</span>
                    <span class="last-update" id="news-update-time">更新中...</span>
                </h2>
                <div id="news-list">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i> 正在获取最新资讯...
                    </div>
                </div>
            </section>
        </div>
    </div>
    
    <!-- 自动刷新按钮 -->
    <button class="auto-refresh active" id="auto-refresh-btn" onclick="toggleAutoRefresh()">
        <i class="fas fa-sync-alt"></i> 自动刷新
    </button>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        class RealTimeStockApp {
            constructor() {
                this.autoRefresh = true;
                this.refreshInterval = null;
                this.lastUpdate = null;
                this.init();
            }
            
            init() {
                this.loadAllData();
                this.startAutoRefresh();
            }
            
            async loadAllData() {
                try {
                    await Promise.all([
                        this.loadMarketData(),
                        this.loadStockRecommendations(),
                        this.loadSectorRecommendations(),
                        this.loadNews()
                    ]);
                } catch (error) {
                    console.error('加载数据失败:', error);
                }
            }
            
            async loadMarketData() {
                try {
                    const response = await fetch('/api/market');
                    const result = await response.json();
                    
                    if (result.status === 'success') {
                        this.renderMarketData(result.data);
                        this.updateTime('market-update-time');
                    } else {
                        this.showError('market-data', '获取市场数据失败');
                    }
                } catch (error) {
                    this.showError('market-data', '网络错误，请稍后重试');
                }
            }
            
            async loadStockRecommendations() {
                try {
                    const response = await fetch('/api/stocks');
                    const result = await response.json();
                    
                    if (result.status === 'success') {
                        this.renderStockRecommendations(result.data);
                        this.updateTime('stock-update-time');
                    } else {
                        this.showError('stock-recommendations', '获取股票推荐失败');
                    }
                } catch (error) {
                    this.showError('stock-recommendations', '网络错误，请稍后重试');
                }
            }
            
            async loadSectorRecommendations() {
                try {
                    const response = await fetch('/api/recommendations');
                    const result = await response.json();
                    
                    if (result.status === 'success' && result.data.sectors) {
                        this.renderSectorRecommendations(result.data.sectors);
                        this.updateTime('sector-update-time');
                    } else {
                        this.showError('sector-recommendations', '获取板块推荐失败');
                    }
                } catch (error) {
                    this.showError('sector-recommendations', '网络错误，请稍后重试');
                }
            }
            
            async loadNews() {
                try {
                    const response = await fetch('/api/recommendations');
                    const result = await response.json();
                    
                    if (result.status === 'success' && result.data.news) {
                        this.renderNews(result.data.news);
                        this.updateTime('news-update-time');
                    } else {
                        this.showError('news-list', '获取新闻失败');
                    }
                } catch (error) {
                    this.showError('news-list', '网络错误，请稍后重试');
                }
            }
            
            renderMarketData(data) {
                const container = document.getElementById('market-data');
                let html = '';
                
                for (const [code, info] of Object.entries(data)) {
                    const changeClass = info.change >= 0 ? 'positive' : 'negative';
                    const changeIcon = info.change >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';
                    const changeSign = info.change >= 0 ? '+' : '';
                    
                    html += `
                        <div class="market-card ${changeClass}">
                            <h4>${info.name}</h4>
                            <div class="market-price">${info.close.toFixed(2)}</div>
                            <div>
                                <i class="fas ${changeIcon}"></i>
                                ${changeSign}${info.change.toFixed(2)} (${changeSign}${info.change_pct.toFixed(2)}%)
                            </div>
                            <small>成交量: ${this.formatVolume(info.volume)}</small>
                        </div>
                    `;
                }
                
                container.innerHTML = html;
            }
            
            renderStockRecommendations(stocks) {
                const container = document.getElementById('stock-recommendations');
                let html = '';
                
                stocks.forEach((stock, index) => {
                    const scoreClass = this.getScoreClass(stock.score);
                    const changeClass = stock.change_pct >= 0 ? 'positive' : 'negative';
                    
                    html += `
                        <div class="recommendation-card stock-card">
                            <div class="card-header">
                                <div>
                                    <div class="card-title">${stock.name}</div>
                                    <small class="text-muted">${stock.code}</small>
                                </div>
                                <span class="score-badge ${scoreClass}">${stock.score}分</span>
                            </div>
                            <div class="indicator-row">
                                <span>当前价格</span>
                                <span>¥${stock.price.toFixed(2)}</span>
                            </div>
                            <div class="indicator-row">
                                <span>涨跌幅</span>
                                <span class="${changeClass}">
                                    ${stock.change_pct >= 0 ? '+' : ''}${stock.change_pct.toFixed(2)}%
                                </span>
                            </div>
                            <div class="reason">
                                <i class="fas fa-lightbulb"></i> ${stock.reason}
                            </div>
                        </div>
                    `;
                });
                
                container.innerHTML = html || '<div class="loading">暂无股票推荐数据</div>';
            }
            
            renderSectorRecommendations(sectors) {
                const container = document.getElementById('sector-recommendations');
                let html = '';
                
                sectors.forEach(sector => {
                    const scoreClass = this.getScoreClass(sector.score);
                    
                    html += `
                        <div class="recommendation-card sector-card">
                            <div class="card-header">
                                <div class="card-title">${sector.name}</div>
                                <span class="score-badge ${scoreClass}">${sector.score}分</span>
                            </div>
                            <div class="indicator-row">
                                <span>平均涨幅</span>
                                <span class="positive">+${sector.change.toFixed(2)}%</span>
                            </div>
                            <div class="reason">
                                <i class="fas fa-lightbulb"></i> ${sector.reason}
                            </div>
                        </div>
                    `;
                });
                
                container.innerHTML = html || '<div class="loading">暂无板块推荐数据</div>';
            }
            
            renderNews(news) {
                const container = document.getElementById('news-list');
                let html = '';
                
                news.forEach(item => {
                    html += `
                        <div class="news-card">
                            <h5>${item.title}</h5>
                            <p style="color: #666; margin: 10px 0;">${item.content}</p>
                            <div style="display: flex; justify-content: space-between; font-size: 0.9rem; color: #999;">
                                <span><i class="fas fa-clock"></i> ${item.time}</span>
                                <span><i class="fas fa-tag"></i> ${item.source}</span>
                            </div>
                        </div>
                    `;
                });
                
                container.innerHTML = html || '<div class="loading">暂无新闻数据</div>';
            }
            
            getScoreClass(score) {
                if (score >= 80) return 'score-excellent';
                if (score >= 70) return 'score-good';
                return 'score-fair';
            }
            
            formatVolume(volume) {
                if (volume >= 100000000) {
                    return (volume / 100000000).toFixed(1) + '亿';
                } else if (volume >= 10000) {
                    return (volume / 10000).toFixed(1) + '万';
                }
                return volume.toString();
            }
            
            updateTime(elementId) {
                const element = document.getElementById(elementId);
                if (element) {
                    element.textContent = `更新时间: ${new Date().toLocaleTimeString()}`;
                }
            }
            
            showError(containerId, message) {
                const container = document.getElementById(containerId);
                container.innerHTML = `<div class="error"><i class="fas fa-exclamation-triangle"></i> ${message}</div>`;
            }
            
            startAutoRefresh() {
                if (this.autoRefresh) {
                    this.refreshInterval = setInterval(() => {
                        this.loadAllData();
                    }, 30000); // 每30秒刷新一次
                }
            }
            
            stopAutoRefresh() {
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                    this.refreshInterval = null;
                }
            }
            
            toggleAutoRefresh() {
                this.autoRefresh = !this.autoRefresh;
                const btn = document.getElementById('auto-refresh-btn');
                
                if (this.autoRefresh) {
                    btn.classList.add('active');
                    btn.innerHTML = '<i class="fas fa-sync-alt"></i> 自动刷新';
                    this.startAutoRefresh();
                } else {
                    btn.classList.remove('active');
                    btn.innerHTML = '<i class="fas fa-pause"></i> 已暂停';
                    this.stopAutoRefresh();
                }
            }
        }
        
        // 全局函数
        function toggleAutoRefresh() {
            app.toggleAutoRefresh();
        }
        
        // 初始化应用
        const app = new RealTimeStockApp();
    </script>
</body>
</html>
