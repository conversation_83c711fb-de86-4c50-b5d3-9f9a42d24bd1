#!/usr/bin/env python3
"""
测试服务器
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json

class TestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            html = '''<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>智能荐股小程序</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .header { background: #007bff; color: white; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .positive { color: green; font-weight: bold; }
        .negative { color: red; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📈 智能荐股小程序</h1>
            <p>基于Tushare数据的股票推荐系统 - 演示版</p>
        </div>
        
        <div class="section">
            <h2>📊 市场概览</h2>
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
                <div style="text-align: center; padding: 15px; background: #e3f2fd; border-radius: 5px;">
                    <h3>上证指数</h3>
                    <div style="font-size: 24px;">3,245.67</div>
                    <div class="positive">+15.23 (+0.47%)</div>
                </div>
                <div style="text-align: center; padding: 15px; background: #e8f5e8; border-radius: 5px;">
                    <h3>深证成指</h3>
                    <div style="font-size: 24px;">12,456.89</div>
                    <div class="positive">+89.45 (+0.72%)</div>
                </div>
                <div style="text-align: center; padding: 15px; background: #fff3e0; border-radius: 5px;">
                    <h3>创业板指</h3>
                    <div style="font-size: 24px;">2,789.34</div>
                    <div class="negative">-12.56 (-0.45%)</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>🔥 热门板块推荐</h2>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                <div style="padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                    <h3>人工智能 <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">85分</span></h3>
                    <div class="positive">平均涨幅: +5.2%</div>
                    <p style="color: #666; font-size: 14px;">政策利好，技术突破，相关概念股表现强势</p>
                </div>
                <div style="padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                    <h3>新能源汽车 <span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">82分</span></h3>
                    <div class="positive">平均涨幅: +4.8%</div>
                    <p style="color: #666; font-size: 14px;">销量增长，产业链完善，龙头企业受益</p>
                </div>
                <div style="padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                    <h3>医疗器械 <span style="background: #17a2b8; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">78分</span></h3>
                    <div class="positive">平均涨幅: +3.5%</div>
                    <p style="color: #666; font-size: 14px;">需求稳定，创新驱动，行业景气度高</p>
                </div>
                <div style="padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                    <h3>半导体 <span style="background: #ffc107; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">75分</span></h3>
                    <div class="positive">平均涨幅: +2.8%</div>
                    <p style="color: #666; font-size: 14px;">国产替代，技术升级，政策支持力度大</p>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>⭐ 精选个股推荐</h2>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                <div style="padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                    <h3>比亚迪 <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">88分</span></h3>
                    <div>002594.SZ | 汽车制造</div>
                    <div>价格: ¥245.80 <span class="positive">(+3.2%)</span></div>
                    <p style="color: #666; font-size: 14px;">新能源汽车龙头，技术领先，市场份额稳定</p>
                </div>
                <div style="padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                    <h3>宁德时代 <span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">85分</span></h3>
                    <div>300750.SZ | 电池制造</div>
                    <div>价格: ¥198.50 <span class="positive">(+2.8%)</span></div>
                    <p style="color: #666; font-size: 14px;">动力电池龙头，全球市场份额领先</p>
                </div>
                <div style="padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                    <h3>贵州茅台 <span style="background: #17a2b8; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">82分</span></h3>
                    <div>600519.SH | 白酒制造</div>
                    <div>价格: ¥1680.00 <span class="positive">(+1.5%)</span></div>
                    <p style="color: #666; font-size: 14px;">白酒龙头，品牌价值突出，盈利能力强</p>
                </div>
                <div style="padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                    <h3>招商银行 <span style="background: #ffc107; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">78分</span></h3>
                    <div>600036.SH | 银行</div>
                    <div>价格: ¥42.80 <span class="positive">(+1.8%)</span></div>
                    <p style="color: #666; font-size: 14px;">零售银行龙头，资产质量优秀</p>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📰 市场资讯</h2>
            <div style="border: 1px solid #ddd; border-radius: 5px; padding: 15px; margin: 10px 0;">
                <h4>A股三大指数集体收涨，新能源板块表现强势</h4>
                <p style="color: #666;">今日A股市场整体表现良好，三大指数均收涨。新能源汽车、光伏等板块涨幅居前，市场情绪回暖...</p>
                <small style="color: #999;">2024-01-15 15:30 | 财经新闻</small>
            </div>
            <div style="border: 1px solid #ddd; border-radius: 5px; padding: 15px; margin: 10px 0;">
                <h4>央行降准释放流动性，市场情绪回暖</h4>
                <p style="color: #666;">央行宣布降准0.5个百分点，释放长期资金约1万亿元，有助于维护市场流动性合理充裕...</p>
                <small style="color: #999;">2024-01-15 14:20 | 金融时报</small>
            </div>
            <div style="border: 1px solid #ddd; border-radius: 5px; padding: 15px; margin: 10px 0;">
                <h4>科技股领涨，人工智能概念持续活跃</h4>
                <p style="color: #666;">人工智能相关概念股今日表现活跃，多只个股涨停。机构认为AI技术应用前景广阔...</p>
                <small style="color: #999;">2024-01-15 13:15 | 证券日报</small>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;">
            <p><strong>⚠️ 风险提示</strong></p>
            <p style="color: #666; font-size: 14px;">本系统仅供学习和研究使用，所有推荐仅供参考，不构成投资建议。<br>股市有风险，投资需谨慎！</p>
        </div>
    </div>
</body>
</html>'''
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(html.encode('utf-8'))
        else:
            self.send_error(404)

if __name__ == '__main__':
    port = 8000
    server = HTTPServer(('localhost', port), TestHandler)
    print(f"服务器启动成功！")
    print(f"访问地址: http://localhost:{port}")
    print("按 Ctrl+C 停止服务")
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n服务已停止")
        server.shutdown()
