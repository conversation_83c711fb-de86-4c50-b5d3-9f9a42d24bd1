Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' 获取当前目录
currentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' 尝试启动Python HTTP服务器
MsgBox "准备启动股票推荐服务器..." & vbCrLf & "目录: " & currentDir, vbInformation, "服务器启动"

' 切换到正确目录并启动服务器
objShell.CurrentDirectory = currentDir
objShell.Run "cmd /k ""cd /d """ & currentDir & """ && python -m http.server 8888""", 1, False

' 等待一下让服务器启动
WScript.Sleep 3000

' 打开浏览器
objShell.Run "http://localhost:8888/final_accurate_stock_app.html", 1, False

MsgBox "服务器已启动!" & vbCrLf & "访问地址: http://localhost:8888/final_accurate_stock_app.html" & vbCrLf & vbCrLf & "如果无法访问，请手动双击 final_accurate_stock_app.html 文件", vbInformation, "启动完成"
