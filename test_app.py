"""
荐股小程序测试版本
用于演示功能，使用模拟数据
"""

from flask import Flask, render_template, jsonify, request
import json
import random
from datetime import datetime, timedelta

app = Flask(__name__)
app.config['SECRET_KEY'] = 'test-secret-key'

# 模拟数据
def generate_mock_market_data():
    """生成模拟市场数据"""
    indices = {
        '000001.SH': {
            'name': '上证指数',
            'close': 3200.50 + random.uniform(-50, 50),
            'change': random.uniform(-30, 30),
            'change_pct': random.uniform(-1.5, 1.5),
            'volume': random.uniform(200000000, 500000000)
        },
        '399001.SZ': {
            'name': '深证成指',
            'close': 12500.30 + random.uniform(-200, 200),
            'change': random.uniform(-100, 100),
            'change_pct': random.uniform(-2, 2),
            'volume': random.uniform(150000000, 400000000)
        },
        '399006.SZ': {
            'name': '创业板指',
            'close': 2800.80 + random.uniform(-100, 100),
            'change': random.uniform(-50, 50),
            'change_pct': random.uniform(-2.5, 2.5),
            'volume': random.uniform(100000000, 300000000)
        }
    }
    return indices

def generate_mock_sector_recommendations():
    """生成模拟板块推荐"""
    sectors = [
        {'name': '人工智能', 'reason': '政策利好，技术突破'},
        {'name': '新能源汽车', 'reason': '销量增长，产业链完善'},
        {'name': '医疗器械', 'reason': '需求稳定，创新驱动'},
        {'name': '半导体', 'reason': '国产替代，技术升级'},
        {'name': '光伏产业', 'reason': '成本下降，需求旺盛'},
        {'name': '5G通信', 'reason': '基建完善，应用拓展'},
        {'name': '生物医药', 'reason': '研发进展，政策支持'},
        {'name': '新材料', 'reason': '下游需求，技术进步'},
        {'name': '环保产业', 'reason': '政策推动，市场扩大'},
        {'name': '消费电子', 'reason': '产品创新，需求回暖'}
    ]
    
    recommendations = []
    for i, sector in enumerate(sectors[:8]):
        recommendations.append({
            'rank': i + 1,
            'sector_code': f'SECTOR_{i+1:03d}',
            'sector_name': sector['name'],
            'stock_count': random.randint(20, 100),
            'avg_change_pct': round(random.uniform(-3, 8), 2),
            'avg_technical_score': round(random.uniform(55, 85), 1),
            'recommendation_score': round(random.uniform(65, 90), 1),
            'recommendation_reason': sector['reason']
        })
    
    return sorted(recommendations, key=lambda x: x['recommendation_score'], reverse=True)

def generate_mock_stock_recommendations():
    """生成模拟个股推荐"""
    stocks = [
        {'name': '比亚迪', 'code': '002594.SZ', 'industry': '汽车制造'},
        {'name': '宁德时代', 'code': '300750.SZ', 'industry': '电池制造'},
        {'name': '贵州茅台', 'code': '600519.SH', 'industry': '白酒制造'},
        {'name': '腾讯控股', 'code': '00700.HK', 'industry': '互联网'},
        {'name': '中国平安', 'code': '601318.SH', 'industry': '保险'},
        {'name': '招商银行', 'code': '600036.SH', 'industry': '银行'},
        {'name': '五粮液', 'code': '000858.SZ', 'industry': '白酒制造'},
        {'name': '美的集团', 'code': '000333.SZ', 'industry': '家电制造'},
        {'name': '中国中免', 'code': '601888.SH', 'industry': '免税零售'},
        {'name': '隆基绿能', 'code': '601012.SH', 'industry': '光伏制造'},
        {'name': '药明康德', 'code': '603259.SH', 'industry': '医药研发'},
        {'name': '海康威视', 'code': '002415.SZ', 'industry': '安防设备'},
        {'name': '立讯精密', 'code': '002475.SZ', 'industry': '电子制造'},
        {'name': '恒瑞医药', 'code': '600276.SH', 'industry': '医药制造'},
        {'name': '万科A', 'code': '000002.SZ', 'industry': '房地产'},
        {'name': '中芯国际', 'code': '688981.SH', 'industry': '半导体'},
        {'name': '京东方A', 'code': '000725.SZ', 'industry': '显示面板'},
        {'name': '三一重工', 'code': '600031.SH', 'industry': '工程机械'},
        {'name': '格力电器', 'code': '000651.SZ', 'industry': '家电制造'},
        {'name': '中国石化', 'code': '600028.SH', 'industry': '石油化工'}
    ]
    
    recommendations = []
    for i, stock in enumerate(stocks[:15]):
        risk_levels = ['较低', '中等', '较高']
        risk_level = random.choice(risk_levels)
        
        recommendations.append({
            'rank': i + 1,
            'ts_code': stock['code'],
            'name': stock['name'],
            'industry': stock['industry'],
            'area': '深圳' if stock['code'].endswith('.SZ') else '上海',
            'current_price': round(random.uniform(10, 200), 2),
            'price_change_pct': round(random.uniform(-5, 8), 2),
            'technical_score': round(random.uniform(60, 90), 1),
            'fundamental_score': round(random.uniform(55, 85), 1),
            'recommendation_score': round(random.uniform(70, 95), 1),
            'market_cap': random.randint(500, 50000),
            'pe_ratio': round(random.uniform(8, 35), 2),
            'pb_ratio': round(random.uniform(0.8, 4.5), 2),
            'recommendation_reason': f'{stock["industry"]}龙头，基本面良好，技术面强势',
            'risk_assessment': {
                'risk_level': risk_level,
                'risk_factors': ['估值合理', '行业景气'] if risk_level == '较低' else ['市场波动'],
                'suggested_position': '标准仓位（3-5%）' if risk_level == '中等' else '小仓位（1-3%）'
            }
        })
    
    return sorted(recommendations, key=lambda x: x['recommendation_score'], reverse=True)

def generate_mock_news():
    """生成模拟新闻"""
    news_titles = [
        "A股三大指数集体收涨，新能源板块表现强势",
        "央行降准释放流动性，市场情绪回暖",
        "科技股领涨，人工智能概念持续活跃",
        "消费板块回调，白酒股分化明显",
        "新能源汽车销量创新高，产业链受益",
        "医药板块震荡上行，创新药获资金关注",
        "房地产政策边际放松，地产股集体上涨",
        "半导体行业景气度提升，龙头股强势反弹",
        "银行股估值修复，大金融板块走强",
        "光伏产业链价格企稳，相关个股表现活跃"
    ]
    
    news_list = []
    for i, title in enumerate(news_titles):
        news_list.append({
            'title': title,
            'content': f'{title}。市场分析认为，当前宏观环境有利于相关板块发展，投资者可关注龙头企业的投资机会。',
            'datetime': (datetime.now() - timedelta(hours=i)).strftime('%Y-%m-%d %H:%M'),
            'source': 'sina'
        })
    
    return news_list

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/market_overview')
def market_overview():
    """市场概览API"""
    try:
        data = generate_mock_market_data()
        return jsonify({
            'status': 'success',
            'data': data,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/recommendations')
def recommendations():
    """推荐API"""
    try:
        rec_type = request.args.get('type', 'sector')
        
        if rec_type == 'sector':
            data = generate_mock_sector_recommendations()
        elif rec_type == 'stock':
            data = generate_mock_stock_recommendations()
        else:
            data = {
                'sectors': generate_mock_sector_recommendations(),
                'stocks': generate_mock_stock_recommendations()
            }
        
        return jsonify({
            'status': 'success',
            'data': data,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/analysis/<stock_code>')
def stock_analysis(stock_code):
    """个股分析API"""
    try:
        # 模拟股票分析数据
        data = {
            'stock_code': stock_code,
            'technical': {
                'price_info': {
                    'current_price': round(random.uniform(10, 200), 2),
                    'price_change': round(random.uniform(-5, 5), 2),
                    'price_change_pct': round(random.uniform(-3, 3), 2),
                    'volume': random.randint(1000000, 100000000)
                },
                'indicators': {
                    'rsi': round(random.uniform(30, 70), 2),
                    'macd': {
                        'macd': round(random.uniform(-1, 1), 3),
                        'signal': round(random.uniform(-1, 1), 3),
                        'histogram': round(random.uniform(-0.5, 0.5), 3)
                    }
                },
                'technical_score': {
                    'score': round(random.uniform(50, 90), 1),
                    'rating': 'buy'
                }
            },
            'fundamental': {
                'valuation': {
                    'pe_ratio': round(random.uniform(10, 30), 2),
                    'pb_ratio': round(random.uniform(1, 4), 2)
                },
                'profitability': {
                    'roe': round(random.uniform(0.05, 0.25), 4)
                },
                'fundamental_score': round(random.uniform(50, 85), 1)
            }
        }
        
        return jsonify({
            'status': 'success',
            'data': data
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/news')
def news():
    """新闻API"""
    try:
        data = generate_mock_news()
        return jsonify({
            'status': 'success',
            'data': data
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

if __name__ == '__main__':
    print("🚀 启动荐股小程序测试版...")
    print("🌐 访问地址: http://localhost:5000")
    print("📊 使用模拟数据进行演示")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
