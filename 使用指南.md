# 智能荐股小程序使用指南

## 🎯 项目概述

本项目是一个基于Tushare数据的智能股票推荐系统，集成了技术分析、基本面分析和智能推荐算法，为投资者提供专业的股票投资建议。

## 📁 项目结构

```
Stock_picking/
├── app.py                 # 主应用程序（完整版）
├── demo.py               # 演示版本（无需外部依赖）
├── test_app.py           # 测试版本（Flask版本）
├── run.py                # 启动脚本
├── config.py             # 配置文件
├── data_fetcher.py       # 数据获取模块
├── analyzer.py           # 数据分析引擎
├── recommender.py        # 推荐策略模块
├── requirements.txt      # 依赖包列表
├── .env                  # 环境变量配置
├── .env.example          # 环境变量示例
├── README.md             # 项目说明
├── templates/            # HTML模板
│   └── index.html
└── static/              # 静态资源
    ├── css/
    │   └── style.css
    └── js/
        └── main.js
```

## 🚀 快速开始

### 方法一：演示版本（推荐）
```bash
# 直接运行演示版本，无需安装依赖
python demo.py
```
然后访问 http://localhost:8000

### 方法二：完整版本
1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 配置Tushare Token：
   - 访问 https://tushare.pro 注册账户
   - 获取API Token
   - 复制 `.env.example` 为 `.env`
   - 在 `.env` 文件中配置 `TUSHARE_TOKEN`

3. 启动应用：
```bash
python run.py
# 或者
python app.py
```

## 🔧 核心功能

### 1. 市场概览
- **实时指数监控**: 上证指数、深证成指、创业板指
- **涨跌幅分析**: 实时价格变动和百分比变化
- **成交量统计**: 市场活跃度指标
- **趋势判断**: 整体市场情绪分析

### 2. 板块推荐
- **热门板块识别**: 基于技术面和资金流向
- **板块轮动分析**: 识别当前热点板块
- **推荐评分**: 综合评分系统（0-100分）
- **推荐理由**: 详细的分析依据

### 3. 个股推荐
- **多维度筛选**: 技术面 + 基本面 + 估值
- **智能评分**: 综合推荐评分系统
- **风险评估**: 风险等级和仓位建议
- **详细分析**: 完整的股票分析报告

### 4. 市场资讯
- **实时新闻**: 最新市场动态
- **资讯整合**: 多源新闻聚合
- **时效性**: 按时间排序的资讯流

## 📊 分析方法

### 技术分析指标
- **移动平均线**: 5日、10日、20日、60日均线系统
- **RSI指标**: 相对强弱指标，判断超买超卖
- **MACD指标**: 指数平滑移动平均线，趋势分析
- **布林带**: 价格波动区间和突破信号
- **成交量分析**: 量价关系和资金流向

### 基本面分析
- **估值指标**: PE、PB、PS等估值倍数
- **盈利能力**: ROE、ROA、毛利率等
- **成长性**: 营收增长率、净利润增长率
- **财务健康**: 资产负债率、流动比率等
- **行业对比**: 同行业相对估值水平

### 推荐算法
```
综合评分 = 技术面评分 × 40% + 基本面评分 × 40% + 估值调整 × 20%

其中：
- 技术面评分：基于多个技术指标的综合评分
- 基本面评分：基于财务指标和盈利能力
- 估值调整：基于PE、PB等估值指标的调整
```

## 🎨 界面功能

### 主要页面元素
1. **导航栏**: 快速访问各个功能模块
2. **市场概览卡片**: 彩色指数卡片，直观显示涨跌
3. **推荐卡片**: 分类显示板块和个股推荐
4. **评分徽章**: 颜色编码的评分系统
5. **新闻卡片**: 简洁的资讯展示

### 交互功能
- **刷新按钮**: 手动更新数据
- **详细分析**: 点击查看个股详细分析
- **响应式设计**: 适配不同屏幕尺寸
- **实时更新**: 自动刷新数据

## 📈 使用建议

### 投资策略
1. **分散投资**: 不要将所有资金投入单一股票
2. **风险控制**: 根据风险评估调整仓位大小
3. **长期持有**: 关注基本面良好的优质股票
4. **止损止盈**: 设置合理的止损和止盈点位

### 系统使用
1. **定期查看**: 建议每日查看推荐更新
2. **结合分析**: 推荐仅供参考，需结合自身判断
3. **关注资讯**: 及时了解市场动态和政策变化
4. **风险意识**: 股市有风险，投资需谨慎

## 🔧 技术架构

### 后端技术
- **Python**: 主要开发语言
- **Flask**: Web框架
- **Pandas**: 数据处理
- **NumPy**: 数值计算
- **Tushare**: 数据源API

### 前端技术
- **HTML5**: 页面结构
- **CSS3**: 样式设计
- **JavaScript**: 交互逻辑
- **Bootstrap**: UI框架
- **Font Awesome**: 图标库

### 数据源
- **Tushare Pro**: 主要数据源
- **实时行情**: 股票价格和指数数据
- **财务数据**: 上市公司财务报表
- **新闻资讯**: 市场新闻和公告

## ⚠️ 重要声明

### 风险提示
- 本系统仅供学习和研究使用
- 所有推荐和分析仅供参考，不构成投资建议
- 股市有风险，投资需谨慎
- 请根据自身风险承受能力做出投资决策
- 历史业绩不代表未来表现

### 免责声明
- 使用本系统产生的任何投资损失，开发者不承担责任
- 用户应当具备相应的投资知识和风险识别能力
- 建议在专业投资顾问指导下进行投资决策

## 🔮 未来规划

### 功能扩展
- [ ] 增加更多技术指标（KDJ、威廉指标等）
- [ ] 优化推荐算法，提高准确性
- [ ] 添加回测功能，验证策略效果
- [ ] 支持自选股管理和监控
- [ ] 增加价格预警和推送功能
- [ ] 开发移动端应用

### 技术优化
- [ ] 数据库存储优化
- [ ] 缓存机制改进
- [ ] 并发处理能力提升
- [ ] API接口标准化
- [ ] 安全性增强

## 📞 技术支持

如有问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- GitHub: https://github.com/your-username/stock-picker
- 技术文档: https://docs.stockpicker.com

---

**祝您投资顺利！** 📈
