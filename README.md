# 智能荐股小程序

基于Tushare数据的智能股票推荐系统，提供技术分析、基本面分析和智能推荐功能。

## 功能特点

### 📊 数据分析
- **技术分析**: RSI、MACD、布林带、移动平均线等技术指标
- **基本面分析**: 财务指标、估值分析、成长性分析
- **趋势分析**: 短期、中期、长期趋势判断
- **支撑阻力**: 自动识别关键价位

### 🎯 智能推荐
- **板块推荐**: 基于板块轮动理论的热门板块推荐
- **个股推荐**: 多维度评分的精选个股推荐
- **风险评估**: 智能风险等级评估和仓位建议
- **推荐理由**: 详细的推荐依据和分析逻辑

### 📈 市场监控
- **实时行情**: 主要指数实时监控
- **市场情绪**: 整体市场趋势判断
- **资讯整合**: 相关市场新闻和资讯

## 安装和配置

### 1. 环境要求
- Python 3.8+
- Tushare Pro账户和Token

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置环境变量
复制 `.env.example` 为 `.env` 并配置：

```bash
# Tushare API Token (必须配置)
TUSHARE_TOKEN=your_tushare_token_here

# 其他配置项（可选）
DATABASE_URL=sqlite:///stock_data.db
REDIS_URL=redis://localhost:6379/0
SECRET_KEY=your-secret-key-here
DEBUG=False
```

### 4. 获取Tushare Token
1. 访问 [Tushare Pro](https://tushare.pro)
2. 注册账户并获取Token
3. 将Token配置到 `.env` 文件中

## 使用方法

### 启动应用
```bash
python app.py
```

应用将在 `http://localhost:5000` 启动

### 主要功能

#### 1. 市场概览
- 查看上证指数、深证成指、创业板指的实时行情
- 了解整体市场趋势和情绪

#### 2. 板块推荐
- 查看当前热门推荐板块
- 了解板块平均涨幅和技术评分
- 获取板块推荐理由

#### 3. 个股推荐
- 查看精选个股推荐列表
- 了解个股技术面和基本面评分
- 获取详细的股票分析报告
- 查看风险评估和仓位建议

#### 4. 市场资讯
- 获取最新的市场新闻和资讯
- 了解市场动态和热点

## API接口

### 市场概览
```
GET /api/market_overview
```

### 推荐数据
```
GET /api/recommendations?type=sector  # 板块推荐
GET /api/recommendations?type=stock   # 个股推荐
GET /api/recommendations?type=all     # 全部推荐
```

### 个股分析
```
GET /api/analysis/{stock_code}
```

### 板块信息
```
GET /api/sectors
```

### 市场资讯
```
GET /api/news
```

## 分析方法

### 技术分析指标
- **移动平均线**: 5日、10日、20日、60日均线
- **RSI**: 相对强弱指标，判断超买超卖
- **MACD**: 指数平滑移动平均线，判断趋势变化
- **布林带**: 判断价格波动区间和突破信号
- **成交量**: 量价关系分析

### 基本面分析
- **估值指标**: 市盈率(PE)、市净率(PB)、市销率(PS)
- **盈利能力**: 净资产收益率(ROE)、总资产收益率(ROA)
- **成长性**: 营收增长率、净利润增长率
- **财务健康**: 资产负债率、流动比率

### 推荐策略
1. **多维度评分**: 技术面40% + 基本面40% + 估值20%
2. **风险控制**: 市值、估值、财务健康度过滤
3. **趋势跟踪**: 结合市场整体趋势调整推荐
4. **板块轮动**: 基于板块相对强度推荐热门板块

## 风险提示

⚠️ **重要声明**：
- 本系统仅供学习和研究使用
- 所有推荐和分析仅供参考，不构成投资建议
- 股市有风险，投资需谨慎
- 请根据自身风险承受能力做出投资决策

## 技术架构

- **后端**: Flask + Python
- **前端**: Bootstrap + JavaScript
- **数据源**: Tushare Pro API
- **分析引擎**: 自研技术分析和基本面分析算法
- **缓存**: 内存缓存提高响应速度

## 开发计划

- [ ] 增加更多技术指标
- [ ] 优化推荐算法
- [ ] 添加回测功能
- [ ] 支持自选股管理
- [ ] 增加预警功能
- [ ] 移动端适配

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- GitHub: https://github.com/your-username/stock-picker

## 许可证

MIT License
