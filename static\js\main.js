// 智能荐股系统前端JavaScript

class StockRecommendationApp {
    constructor() {
        this.init();
    }

    init() {
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            this.loadMarketOverview();
            this.loadSectorRecommendations();
            this.loadStockRecommendations();
            this.loadNews();
        });
    }

    // 显示加载状态
    showLoading(containerId) {
        const container = document.getElementById(containerId);
        container.innerHTML = `
            <div class="col-12 text-center loading-container">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">正在获取数据...</p>
            </div>
        `;
    }

    // 显示错误信息
    showError(containerId, message) {
        const container = document.getElementById(containerId);
        container.innerHTML = `
            <div class="col-12">
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                </div>
            </div>
        `;
    }

    // 加载市场概览
    async loadMarketOverview() {
        this.showLoading('market-indices');
        
        try {
            const response = await fetch('/api/market_overview');
            const result = await response.json();
            
            if (result.status === 'success') {
                this.renderMarketOverview(result.data);
            } else {
                this.showError('market-indices', result.message || '获取市场数据失败');
            }
        } catch (error) {
            console.error('加载市场概览失败:', error);
            this.showError('market-indices', '网络错误，请稍后重试');
        }
    }

    // 渲染市场概览
    renderMarketOverview(data) {
        const container = document.getElementById('market-indices');
        let html = '';

        for (const [indexCode, indexData] of Object.entries(data)) {
            const changeClass = indexData.change >= 0 ? 'positive' : 'negative';
            const changeIcon = indexData.change >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';
            const changeSign = indexData.change >= 0 ? '+' : '';

            html += `
                <div class="col-md-4 mb-3">
                    <div class="market-index-card ${changeClass} fade-in-up">
                        <div class="index-name">${indexData.name}</div>
                        <div class="index-price">${indexData.close.toFixed(2)}</div>
                        <div class="index-change">
                            <i class="fas ${changeIcon} me-2"></i>
                            ${changeSign}${indexData.change.toFixed(2)} 
                            (${changeSign}${indexData.change_pct.toFixed(2)}%)
                        </div>
                        <div class="mt-2">
                            <small>成交量: ${this.formatVolume(indexData.volume)}</small>
                        </div>
                    </div>
                </div>
            `;
        }

        container.innerHTML = html;
    }

    // 加载板块推荐
    async loadSectorRecommendations() {
        this.showLoading('sector-list');
        
        try {
            const response = await fetch('/api/recommendations?type=sector');
            const result = await response.json();
            
            if (result.status === 'success') {
                this.renderSectorRecommendations(result.data);
            } else {
                this.showError('sector-list', result.message || '获取板块推荐失败');
            }
        } catch (error) {
            console.error('加载板块推荐失败:', error);
            this.showError('sector-list', '网络错误，请稍后重试');
        }
    }

    // 渲染板块推荐
    renderSectorRecommendations(sectors) {
        const container = document.getElementById('sector-list');
        let html = '';

        sectors.forEach((sector, index) => {
            const scoreClass = this.getScoreClass(sector.recommendation_score);
            
            html += `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="recommendation-card sector fade-in-up">
                        <div class="card-header">
                            <div>
                                <div class="card-title">${sector.sector_name}</div>
                                <small class="text-muted">成分股: ${sector.stock_count}只</small>
                            </div>
                            <div class="recommendation-rank">${sector.rank}</div>
                        </div>
                        
                        <div class="indicator-row">
                            <span class="indicator-label">推荐评分</span>
                            <span class="score-badge ${scoreClass}">
                                ${sector.recommendation_score.toFixed(1)}分
                            </span>
                        </div>
                        
                        <div class="indicator-row">
                            <span class="indicator-label">平均涨幅</span>
                            <span class="indicator-value ${sector.avg_change_pct >= 0 ? 'positive-value' : 'negative-value'}">
                                ${sector.avg_change_pct >= 0 ? '+' : ''}${sector.avg_change_pct}%
                            </span>
                        </div>
                        
                        <div class="indicator-row">
                            <span class="indicator-label">技术评分</span>
                            <span class="indicator-value">${sector.avg_technical_score.toFixed(1)}分</span>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-lightbulb me-1"></i>
                                ${sector.recommendation_reason}
                            </small>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html || '<div class="col-12"><p class="text-center text-muted">暂无板块推荐数据</p></div>';
    }

    // 加载个股推荐
    async loadStockRecommendations() {
        this.showLoading('stock-list');
        
        try {
            const response = await fetch('/api/recommendations?type=stock');
            const result = await response.json();
            
            if (result.status === 'success') {
                this.renderStockRecommendations(result.data);
            } else {
                this.showError('stock-list', result.message || '获取个股推荐失败');
            }
        } catch (error) {
            console.error('加载个股推荐失败:', error);
            this.showError('stock-list', '网络错误，请稍后重试');
        }
    }

    // 渲染个股推荐
    renderStockRecommendations(stocks) {
        const container = document.getElementById('stock-list');
        let html = '';

        stocks.forEach((stock, index) => {
            const scoreClass = this.getScoreClass(stock.recommendation_score);
            const priceChangeClass = stock.price_change_pct >= 0 ? 'positive-value' : 'negative-value';
            const riskClass = this.getRiskClass(stock.risk_assessment?.risk_level);
            
            html += `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="recommendation-card stock fade-in-up">
                        <div class="card-header">
                            <div>
                                <div class="card-title">${stock.name}</div>
                                <small class="text-muted">${stock.ts_code} | ${stock.industry}</small>
                            </div>
                            <div class="recommendation-rank">${stock.rank}</div>
                        </div>
                        
                        <div class="indicator-row">
                            <span class="indicator-label">当前价格</span>
                            <span class="indicator-value">¥${stock.current_price.toFixed(2)}</span>
                        </div>
                        
                        <div class="indicator-row">
                            <span class="indicator-label">涨跌幅</span>
                            <span class="indicator-value ${priceChangeClass}">
                                ${stock.price_change_pct >= 0 ? '+' : ''}${stock.price_change_pct.toFixed(2)}%
                            </span>
                        </div>
                        
                        <div class="indicator-row">
                            <span class="indicator-label">推荐评分</span>
                            <span class="score-badge ${scoreClass}">
                                ${stock.recommendation_score.toFixed(1)}分
                            </span>
                        </div>
                        
                        <div class="indicator-row">
                            <span class="indicator-label">风险等级</span>
                            <span class="indicator-value ${riskClass}">
                                ${stock.risk_assessment?.risk_level || '中等'}
                            </span>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-lightbulb me-1"></i>
                                ${stock.recommendation_reason}
                            </small>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-detail btn-sm" onclick="showStockDetail('${stock.ts_code}')">
                                <i class="fas fa-chart-line me-1"></i>详细分析
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html || '<div class="col-12"><p class="text-center text-muted">暂无个股推荐数据</p></div>';
    }

    // 加载新闻资讯
    async loadNews() {
        this.showLoading('news-list');
        
        try {
            const response = await fetch('/api/news');
            const result = await response.json();
            
            if (result.status === 'success') {
                this.renderNews(result.data);
            } else {
                this.showError('news-list', result.message || '获取新闻失败');
            }
        } catch (error) {
            console.error('加载新闻失败:', error);
            this.showError('news-list', '网络错误，请稍后重试');
        }
    }

    // 渲染新闻
    renderNews(news) {
        const container = document.getElementById('news-list');
        let html = '';

        news.forEach((item, index) => {
            html += `
                <div class="col-md-6 mb-3">
                    <div class="news-card fade-in-up">
                        <div class="news-title">${item.title}</div>
                        <div class="news-content">${item.content}</div>
                        <div class="news-meta">
                            <span><i class="fas fa-clock me-1"></i>${item.datetime}</span>
                            <span><i class="fas fa-tag me-1"></i>${item.source}</span>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html || '<div class="col-12"><p class="text-center text-muted">暂无新闻数据</p></div>';
    }

    // 获取评分样式类
    getScoreClass(score) {
        if (score >= 80) return 'score-excellent';
        if (score >= 70) return 'score-good';
        if (score >= 60) return 'score-fair';
        return 'score-poor';
    }

    // 获取风险等级样式类
    getRiskClass(riskLevel) {
        switch (riskLevel) {
            case '较低': return 'risk-low';
            case '较高': return 'risk-high';
            default: return 'risk-medium';
        }
    }

    // 格式化成交量
    formatVolume(volume) {
        if (volume >= 100000000) {
            return (volume / 100000000).toFixed(1) + '亿';
        } else if (volume >= 10000) {
            return (volume / 10000).toFixed(1) + '万';
        }
        return volume.toString();
    }
}

// 显示股票详情
async function showStockDetail(tsCode) {
    const modal = new bootstrap.Modal(document.getElementById('stockDetailModal'));
    const content = document.getElementById('stock-detail-content');
    
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在获取详细分析...</p>
        </div>
    `;
    
    modal.show();
    
    try {
        const response = await fetch(`/api/analysis/${tsCode}`);
        const result = await response.json();
        
        if (result.status === 'success') {
            renderStockDetail(result.data);
        } else {
            content.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${result.message || '获取股票详情失败'}
                </div>
            `;
        }
    } catch (error) {
        console.error('获取股票详情失败:', error);
        content.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                网络错误，请稍后重试
            </div>
        `;
    }
}

// 渲染股票详情
function renderStockDetail(data) {
    const content = document.getElementById('stock-detail-content');
    const technical = data.technical || {};
    const fundamental = data.fundamental || {};
    const priceInfo = technical.price_info || {};
    
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-chart-line me-2"></i>技术分析</h6>
                <div class="indicator-row">
                    <span class="indicator-label">当前价格</span>
                    <span class="indicator-value">¥${(priceInfo.current_price || 0).toFixed(2)}</span>
                </div>
                <div class="indicator-row">
                    <span class="indicator-label">涨跌幅</span>
                    <span class="indicator-value ${(priceInfo.price_change_pct || 0) >= 0 ? 'positive-value' : 'negative-value'}">
                        ${(priceInfo.price_change_pct || 0) >= 0 ? '+' : ''}${(priceInfo.price_change_pct || 0).toFixed(2)}%
                    </span>
                </div>
                <div class="indicator-row">
                    <span class="indicator-label">RSI</span>
                    <span class="indicator-value">${(technical.indicators?.rsi || 0).toFixed(2)}</span>
                </div>
                <div class="indicator-row">
                    <span class="indicator-label">技术评分</span>
                    <span class="indicator-value">${(technical.technical_score?.score || 0).toFixed(1)}分</span>
                </div>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-building me-2"></i>基本面分析</h6>
                <div class="indicator-row">
                    <span class="indicator-label">市盈率</span>
                    <span class="indicator-value">${(fundamental.valuation?.pe_ratio || 0).toFixed(2)}</span>
                </div>
                <div class="indicator-row">
                    <span class="indicator-label">市净率</span>
                    <span class="indicator-value">${(fundamental.valuation?.pb_ratio || 0).toFixed(2)}</span>
                </div>
                <div class="indicator-row">
                    <span class="indicator-label">净资产收益率</span>
                    <span class="indicator-value">${((fundamental.profitability?.roe || 0) * 100).toFixed(2)}%</span>
                </div>
                <div class="indicator-row">
                    <span class="indicator-label">基本面评分</span>
                    <span class="indicator-value">${(fundamental.fundamental_score || 0).toFixed(1)}分</span>
                </div>
            </div>
        </div>
    `;
}

// 刷新函数
function refreshMarketData() {
    app.loadMarketOverview();
}

function refreshSectorRecommendations() {
    app.loadSectorRecommendations();
}

function refreshStockRecommendations() {
    app.loadStockRecommendations();
}

function refreshNews() {
    app.loadNews();
}

// 初始化应用
const app = new StockRecommendationApp();
