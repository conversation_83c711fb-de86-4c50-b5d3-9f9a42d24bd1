"""
数据获取模块
封装Tushare API接口，提供股票数据获取功能
"""

import tushare as ts
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import time
from typing import Optional, List, Dict, Any
import requests

from config import Config

logger = logging.getLogger(__name__)

class DataFetcher:
    """数据获取器"""
    
    def __init__(self):
        """初始化数据获取器"""
        self.token = Config.TUSHARE_TOKEN
        if self.token and self.token != 'your_tushare_token_here':
            ts.set_token(self.token)
            self.pro = ts.pro_api()
        else:
            logger.warning("Tushare token未配置，部分功能可能无法使用")
            self.pro = None
        
        # 缓存
        self._cache = {}
        self._cache_timeout = 300  # 5分钟缓存
    
    def _get_cache_key(self, func_name: str, **kwargs) -> str:
        """生成缓存键"""
        key_parts = [func_name]
        for k, v in sorted(kwargs.items()):
            key_parts.append(f"{k}={v}")
        return "|".join(key_parts)
    
    def _get_from_cache(self, cache_key: str) -> Optional[Any]:
        """从缓存获取数据"""
        if cache_key in self._cache:
            data, timestamp = self._cache[cache_key]
            if time.time() - timestamp < self._cache_timeout:
                return data
            else:
                del self._cache[cache_key]
        return None
    
    def _set_cache(self, cache_key: str, data: Any):
        """设置缓存"""
        self._cache[cache_key] = (data, time.time())
    
    def get_stock_list(self) -> pd.DataFrame:
        """获取股票列表"""
        cache_key = self._get_cache_key('stock_list')
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data
        
        try:
            if not self.pro:
                logger.error("Tushare API未初始化")
                return pd.DataFrame()
            
            # 获取股票基本信息
            stock_basic = self.pro.stock_basic(
                exchange='',
                list_status='L',
                fields='ts_code,symbol,name,area,industry,market,list_date'
            )
            
            self._set_cache(cache_key, stock_basic)
            logger.info(f"获取到 {len(stock_basic)} 只股票信息")
            return stock_basic
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {str(e)}")
            return pd.DataFrame()
    
    def get_stock_daily(self, ts_code: str, days: int = 30) -> pd.DataFrame:
        """获取股票日线数据"""
        cache_key = self._get_cache_key('stock_daily', ts_code=ts_code, days=days)
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data
        
        try:
            if not self.pro:
                logger.error("Tushare API未初始化")
                return pd.DataFrame()
            
            # 计算开始日期
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=days*2)).strftime('%Y%m%d')
            
            # 获取日线数据
            daily_data = self.pro.daily(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if not daily_data.empty:
                daily_data = daily_data.sort_values('trade_date').reset_index(drop=True)
                daily_data['trade_date'] = pd.to_datetime(daily_data['trade_date'])
                
                # 获取最近N天的数据
                daily_data = daily_data.tail(days)
            
            self._set_cache(cache_key, daily_data)
            return daily_data
            
        except Exception as e:
            logger.error(f"获取股票 {ts_code} 日线数据失败: {str(e)}")
            return pd.DataFrame()
    
    def get_stock_basic_info(self, ts_code: str) -> Dict[str, Any]:
        """获取股票基本信息"""
        cache_key = self._get_cache_key('stock_basic_info', ts_code=ts_code)
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data
        
        try:
            if not self.pro:
                return {}
            
            # 获取基本信息
            basic_info = self.pro.stock_basic(ts_code=ts_code)
            
            # 获取每日指标
            daily_basic = self.pro.daily_basic(
                ts_code=ts_code,
                trade_date=datetime.now().strftime('%Y%m%d'),
                fields='ts_code,trade_date,pe,pb,ps,dv_ratio,dv_ttm,total_share,float_share,total_mv,circ_mv'
            )
            
            result = {}
            if not basic_info.empty:
                result.update(basic_info.iloc[0].to_dict())
            
            if not daily_basic.empty:
                result.update(daily_basic.iloc[0].to_dict())
            
            self._set_cache(cache_key, result)
            return result
            
        except Exception as e:
            logger.error(f"获取股票 {ts_code} 基本信息失败: {str(e)}")
            return {}
    
    def get_financial_data(self, ts_code: str) -> Dict[str, Any]:
        """获取财务数据"""
        cache_key = self._get_cache_key('financial_data', ts_code=ts_code)
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data
        
        try:
            if not self.pro:
                return {}
            
            # 获取最近的财务指标
            fina_indicator = self.pro.fina_indicator(
                ts_code=ts_code,
                limit=4,  # 最近4个季度
                fields='ts_code,end_date,roe,roa,gross_profit_margin,net_profit_margin,debt_to_assets,current_ratio'
            )
            
            # 获取利润表
            income = self.pro.income(
                ts_code=ts_code,
                limit=4,
                fields='ts_code,end_date,total_revenue,revenue,total_profit,n_income'
            )
            
            result = {
                'indicators': fina_indicator.to_dict('records') if not fina_indicator.empty else [],
                'income': income.to_dict('records') if not income.empty else []
            }
            
            self._set_cache(cache_key, result)
            return result
            
        except Exception as e:
            logger.error(f"获取股票 {ts_code} 财务数据失败: {str(e)}")
            return {}
    
    def get_sector_data(self) -> List[Dict[str, Any]]:
        """获取板块数据"""
        cache_key = self._get_cache_key('sector_data')
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data
        
        try:
            if not self.pro:
                return []
            
            # 获取概念分类
            concept_list = self.pro.concept(fields='code,name')
            
            sectors = []
            for _, concept in concept_list.iterrows():
                # 获取概念成分股
                concept_detail = self.pro.concept_detail(id=concept['code'])
                
                if not concept_detail.empty:
                    sectors.append({
                        'code': concept['code'],
                        'name': concept['name'],
                        'stock_count': len(concept_detail),
                        'stocks': concept_detail['ts_code'].tolist()[:10]  # 只取前10只
                    })
            
            self._set_cache(cache_key, sectors)
            return sectors
            
        except Exception as e:
            logger.error(f"获取板块数据失败: {str(e)}")
            return []
    
    def get_index_daily(self, ts_code: str, days: int = 30) -> pd.DataFrame:
        """获取指数日线数据"""
        cache_key = self._get_cache_key('index_daily', ts_code=ts_code, days=days)
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data
        
        try:
            if not self.pro:
                return pd.DataFrame()
            
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=days*2)).strftime('%Y%m%d')
            
            index_data = self.pro.index_daily(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if not index_data.empty:
                index_data = index_data.sort_values('trade_date').reset_index(drop=True)
                index_data['trade_date'] = pd.to_datetime(index_data['trade_date'])
                index_data = index_data.tail(days)
            
            self._set_cache(cache_key, index_data)
            return index_data
            
        except Exception as e:
            logger.error(f"获取指数 {ts_code} 数据失败: {str(e)}")
            return pd.DataFrame()
    
    def get_index_name(self, ts_code: str) -> str:
        """获取指数名称"""
        index_names = {
            '000001.SH': '上证指数',
            '399001.SZ': '深证成指',
            '399006.SZ': '创业板指'
        }
        return index_names.get(ts_code, ts_code)
    
    def get_news_data(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取新闻数据"""
        cache_key = self._get_cache_key('news_data', limit=limit)
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data
        
        try:
            if not self.pro:
                return []
            
            # 获取新闻数据
            news = self.pro.news(
                src='sina',
                start_date=(datetime.now() - timedelta(days=7)).strftime('%Y%m%d'),
                end_date=datetime.now().strftime('%Y%m%d'),
                limit=limit
            )
            
            news_list = []
            if not news.empty:
                for _, item in news.iterrows():
                    news_list.append({
                        'title': item.get('title', ''),
                        'content': item.get('content', '')[:200] + '...',  # 截取前200字符
                        'datetime': item.get('datetime', ''),
                        'source': item.get('source', 'sina')
                    })
            
            self._set_cache(cache_key, news_list)
            return news_list
            
        except Exception as e:
            logger.error(f"获取新闻数据失败: {str(e)}")
            return []
