"""
股票分析引擎
包含技术分析和基本面分析功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Tuple
import logging
from datetime import datetime, timedelta

from config import Config

logger = logging.getLogger(__name__)

class StockAnalyzer:
    """股票分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.analysis_params = Config.ANALYSIS_PARAMS
    
    def calculate_ma(self, data: pd.DataFrame, periods: List[int]) -> pd.DataFrame:
        """计算移动平均线"""
        result = data.copy()
        for period in periods:
            result[f'ma_{period}'] = result['close'].rolling(window=period).mean()
        return result
    
    def calculate_rsi(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_macd(self, data: pd.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """计算MACD指标"""
        exp1 = data['close'].ewm(span=fast).mean()
        exp2 = data['close'].ewm(span=slow).mean()
        macd = exp1 - exp2
        signal_line = macd.ewm(span=signal).mean()
        histogram = macd - signal_line
        
        return {
            'macd': macd,
            'signal': signal_line,
            'histogram': histogram
        }
    
    def calculate_bollinger_bands(self, data: pd.DataFrame, period: int = 20, std_dev: int = 2) -> Dict[str, pd.Series]:
        """计算布林带"""
        sma = data['close'].rolling(window=period).mean()
        std = data['close'].rolling(window=period).std()
        
        return {
            'upper': sma + (std * std_dev),
            'middle': sma,
            'lower': sma - (std * std_dev)
        }
    
    def calculate_volume_indicators(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """计算成交量指标"""
        volume_ma = data['vol'].rolling(window=self.analysis_params['volume_ma_period']).mean()
        volume_ratio = data['vol'] / volume_ma
        
        return {
            'volume_ma': volume_ma,
            'volume_ratio': volume_ratio
        }
    
    def technical_analysis(self, data: pd.DataFrame) -> Dict[str, Any]:
        """技术分析"""
        try:
            if data.empty or len(data) < 20:
                return {'error': '数据不足，无法进行技术分析'}
            
            # 确保数据按日期排序
            data = data.sort_values('trade_date').reset_index(drop=True)
            
            # 计算技术指标
            data_with_ma = self.calculate_ma(data, self.analysis_params['ma_periods'])
            rsi = self.calculate_rsi(data, self.analysis_params['rsi_period'])
            macd_data = self.calculate_macd(data, *self.analysis_params['macd_params'])
            bollinger = self.calculate_bollinger_bands(data, self.analysis_params['bollinger_period'])
            volume_indicators = self.calculate_volume_indicators(data)
            
            # 获取最新数据
            latest = data.iloc[-1]
            prev = data.iloc[-2] if len(data) > 1 else latest
            
            # 价格分析
            price_change = latest['close'] - prev['close']
            price_change_pct = (price_change / prev['close']) * 100
            
            # 趋势分析
            trend_analysis = self._analyze_trend(data_with_ma)
            
            # 支撑阻力位分析
            support_resistance = self._analyze_support_resistance(data)
            
            # 成交量分析
            volume_analysis = self._analyze_volume(data, volume_indicators)
            
            # 综合评分
            technical_score = self._calculate_technical_score(
                data_with_ma, rsi.iloc[-1], macd_data, bollinger, latest
            )
            
            return {
                'price_info': {
                    'current_price': float(latest['close']),
                    'price_change': float(price_change),
                    'price_change_pct': float(price_change_pct),
                    'volume': float(latest['vol']),
                    'turnover': float(latest.get('amount', 0))
                },
                'indicators': {
                    'rsi': float(rsi.iloc[-1]) if not pd.isna(rsi.iloc[-1]) else None,
                    'macd': {
                        'macd': float(macd_data['macd'].iloc[-1]) if not pd.isna(macd_data['macd'].iloc[-1]) else None,
                        'signal': float(macd_data['signal'].iloc[-1]) if not pd.isna(macd_data['signal'].iloc[-1]) else None,
                        'histogram': float(macd_data['histogram'].iloc[-1]) if not pd.isna(macd_data['histogram'].iloc[-1]) else None
                    },
                    'bollinger': {
                        'upper': float(bollinger['upper'].iloc[-1]) if not pd.isna(bollinger['upper'].iloc[-1]) else None,
                        'middle': float(bollinger['middle'].iloc[-1]) if not pd.isna(bollinger['middle'].iloc[-1]) else None,
                        'lower': float(bollinger['lower'].iloc[-1]) if not pd.isna(bollinger['lower'].iloc[-1]) else None
                    },
                    'moving_averages': {
                        f'ma_{period}': float(data_with_ma[f'ma_{period}'].iloc[-1]) 
                        if not pd.isna(data_with_ma[f'ma_{period}'].iloc[-1]) else None
                        for period in self.analysis_params['ma_periods']
                    }
                },
                'trend_analysis': trend_analysis,
                'support_resistance': support_resistance,
                'volume_analysis': volume_analysis,
                'technical_score': technical_score
            }
            
        except Exception as e:
            logger.error(f"技术分析失败: {str(e)}")
            return {'error': str(e)}
    
    def _analyze_trend(self, data: pd.DataFrame) -> Dict[str, Any]:
        """趋势分析"""
        try:
            latest = data.iloc[-1]
            
            # 短期趋势（5日均线 vs 10日均线）
            short_trend = 'up' if latest['ma_5'] > latest['ma_10'] else 'down'
            
            # 中期趋势（10日均线 vs 20日均线）
            medium_trend = 'up' if latest['ma_10'] > latest['ma_20'] else 'down'
            
            # 长期趋势（20日均线 vs 60日均线）
            long_trend = 'up' if latest['ma_20'] > latest['ma_60'] else 'down'
            
            # 价格相对于均线的位置
            price_vs_ma = {
                'above_ma5': latest['close'] > latest['ma_5'],
                'above_ma10': latest['close'] > latest['ma_10'],
                'above_ma20': latest['close'] > latest['ma_20'],
                'above_ma60': latest['close'] > latest['ma_60']
            }
            
            return {
                'short_trend': short_trend,
                'medium_trend': medium_trend,
                'long_trend': long_trend,
                'price_vs_ma': price_vs_ma,
                'overall_trend': self._get_overall_trend(short_trend, medium_trend, long_trend)
            }
            
        except Exception as e:
            logger.error(f"趋势分析失败: {str(e)}")
            return {}
    
    def _get_overall_trend(self, short: str, medium: str, long: str) -> str:
        """获取整体趋势"""
        up_count = sum([1 for trend in [short, medium, long] if trend == 'up'])
        if up_count >= 2:
            return 'bullish'
        elif up_count <= 1:
            return 'bearish'
        else:
            return 'neutral'
    
    def _analyze_support_resistance(self, data: pd.DataFrame) -> Dict[str, Any]:
        """支撑阻力位分析"""
        try:
            # 计算近期高低点
            highs = data['high'].rolling(window=5).max()
            lows = data['low'].rolling(window=5).min()
            
            # 找出支撑位和阻力位
            resistance_levels = highs.nlargest(3).tolist()
            support_levels = lows.nsmallest(3).tolist()
            
            current_price = data['close'].iloc[-1]
            
            return {
                'resistance_levels': [float(level) for level in resistance_levels if not pd.isna(level)],
                'support_levels': [float(level) for level in support_levels if not pd.isna(level)],
                'current_price': float(current_price)
            }
            
        except Exception as e:
            logger.error(f"支撑阻力位分析失败: {str(e)}")
            return {}
    
    def _analyze_volume(self, data: pd.DataFrame, volume_indicators: Dict[str, pd.Series]) -> Dict[str, Any]:
        """成交量分析"""
        try:
            latest_volume = data['vol'].iloc[-1]
            avg_volume = volume_indicators['volume_ma'].iloc[-1]
            volume_ratio = volume_indicators['volume_ratio'].iloc[-1]
            
            # 成交量趋势
            volume_trend = 'increasing' if volume_ratio > 1.2 else 'decreasing' if volume_ratio < 0.8 else 'stable'
            
            return {
                'current_volume': float(latest_volume),
                'average_volume': float(avg_volume) if not pd.isna(avg_volume) else None,
                'volume_ratio': float(volume_ratio) if not pd.isna(volume_ratio) else None,
                'volume_trend': volume_trend
            }
            
        except Exception as e:
            logger.error(f"成交量分析失败: {str(e)}")
            return {}
    
    def _calculate_technical_score(self, data: pd.DataFrame, rsi: float, macd_data: Dict, 
                                 bollinger: Dict, latest: pd.Series) -> Dict[str, Any]:
        """计算技术评分"""
        try:
            score = 50  # 基础分数
            signals = []
            
            # RSI评分
            if not pd.isna(rsi):
                if rsi < 30:
                    score += 10
                    signals.append('RSI超卖')
                elif rsi > 70:
                    score -= 10
                    signals.append('RSI超买')
            
            # MACD评分
            if not pd.isna(macd_data['macd'].iloc[-1]) and not pd.isna(macd_data['signal'].iloc[-1]):
                if macd_data['macd'].iloc[-1] > macd_data['signal'].iloc[-1]:
                    score += 5
                    signals.append('MACD金叉')
                else:
                    score -= 5
                    signals.append('MACD死叉')
            
            # 布林带评分
            if not pd.isna(bollinger['lower'].iloc[-1]) and not pd.isna(bollinger['upper'].iloc[-1]):
                if latest['close'] < bollinger['lower'].iloc[-1]:
                    score += 8
                    signals.append('价格触及布林带下轨')
                elif latest['close'] > bollinger['upper'].iloc[-1]:
                    score -= 8
                    signals.append('价格触及布林带上轨')
            
            # 均线评分
            ma_score = 0
            for period in self.analysis_params['ma_periods']:
                if not pd.isna(data[f'ma_{period}'].iloc[-1]):
                    if latest['close'] > data[f'ma_{period}'].iloc[-1]:
                        ma_score += 2
                    else:
                        ma_score -= 2
            
            score += ma_score
            
            # 限制评分范围
            score = max(0, min(100, score))
            
            # 评级
            if score >= 70:
                rating = 'strong_buy'
            elif score >= 60:
                rating = 'buy'
            elif score >= 40:
                rating = 'hold'
            elif score >= 30:
                rating = 'sell'
            else:
                rating = 'strong_sell'
            
            return {
                'score': score,
                'rating': rating,
                'signals': signals
            }
            
        except Exception as e:
            logger.error(f"技术评分计算失败: {str(e)}")
            return {'score': 50, 'rating': 'hold', 'signals': []}
    
    def fundamental_analysis(self, basic_info: Dict[str, Any], financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """基本面分析"""
        try:
            analysis_result = {
                'valuation': {},
                'profitability': {},
                'growth': {},
                'financial_health': {},
                'fundamental_score': 50
            }
            
            # 估值分析
            if 'pe' in basic_info and basic_info['pe']:
                pe_ratio = float(basic_info['pe'])
                analysis_result['valuation']['pe_ratio'] = pe_ratio
                analysis_result['valuation']['pe_evaluation'] = self._evaluate_pe(pe_ratio)
            
            if 'pb' in basic_info and basic_info['pb']:
                pb_ratio = float(basic_info['pb'])
                analysis_result['valuation']['pb_ratio'] = pb_ratio
                analysis_result['valuation']['pb_evaluation'] = self._evaluate_pb(pb_ratio)
            
            # 盈利能力分析
            if financial_data.get('indicators'):
                latest_indicator = financial_data['indicators'][0]
                
                if 'roe' in latest_indicator and latest_indicator['roe']:
                    roe = float(latest_indicator['roe'])
                    analysis_result['profitability']['roe'] = roe
                    analysis_result['profitability']['roe_evaluation'] = self._evaluate_roe(roe)
                
                if 'roa' in latest_indicator and latest_indicator['roa']:
                    roa = float(latest_indicator['roa'])
                    analysis_result['profitability']['roa'] = roa
                
                if 'gross_profit_margin' in latest_indicator and latest_indicator['gross_profit_margin']:
                    gpm = float(latest_indicator['gross_profit_margin'])
                    analysis_result['profitability']['gross_profit_margin'] = gpm
            
            # 成长性分析
            if financial_data.get('income') and len(financial_data['income']) >= 2:
                income_data = financial_data['income']
                latest_revenue = income_data[0].get('total_revenue', 0)
                prev_revenue = income_data[1].get('total_revenue', 0)
                
                if latest_revenue and prev_revenue:
                    revenue_growth = (float(latest_revenue) - float(prev_revenue)) / float(prev_revenue)
                    analysis_result['growth']['revenue_growth'] = revenue_growth
                    analysis_result['growth']['revenue_growth_evaluation'] = self._evaluate_growth(revenue_growth)
            
            # 财务健康度分析
            if financial_data.get('indicators'):
                latest_indicator = financial_data['indicators'][0]
                
                if 'debt_to_assets' in latest_indicator and latest_indicator['debt_to_assets']:
                    debt_ratio = float(latest_indicator['debt_to_assets'])
                    analysis_result['financial_health']['debt_to_assets'] = debt_ratio
                    analysis_result['financial_health']['debt_evaluation'] = self._evaluate_debt(debt_ratio)
                
                if 'current_ratio' in latest_indicator and latest_indicator['current_ratio']:
                    current_ratio = float(latest_indicator['current_ratio'])
                    analysis_result['financial_health']['current_ratio'] = current_ratio
                    analysis_result['financial_health']['liquidity_evaluation'] = self._evaluate_liquidity(current_ratio)
            
            # 计算基本面综合评分
            analysis_result['fundamental_score'] = self._calculate_fundamental_score(analysis_result)
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"基本面分析失败: {str(e)}")
            return {'error': str(e)}
    
    def _evaluate_pe(self, pe_ratio: float) -> str:
        """评估市盈率"""
        if pe_ratio < 0:
            return 'negative'
        elif pe_ratio < 15:
            return 'undervalued'
        elif pe_ratio < 25:
            return 'fair'
        elif pe_ratio < 40:
            return 'overvalued'
        else:
            return 'highly_overvalued'
    
    def _evaluate_pb(self, pb_ratio: float) -> str:
        """评估市净率"""
        if pb_ratio < 1:
            return 'undervalued'
        elif pb_ratio < 2:
            return 'fair'
        elif pb_ratio < 3:
            return 'overvalued'
        else:
            return 'highly_overvalued'
    
    def _evaluate_roe(self, roe: float) -> str:
        """评估净资产收益率"""
        if roe > 0.15:
            return 'excellent'
        elif roe > 0.10:
            return 'good'
        elif roe > 0.05:
            return 'fair'
        else:
            return 'poor'
    
    def _evaluate_growth(self, growth_rate: float) -> str:
        """评估增长率"""
        if growth_rate > 0.20:
            return 'high_growth'
        elif growth_rate > 0.10:
            return 'moderate_growth'
        elif growth_rate > 0:
            return 'slow_growth'
        else:
            return 'negative_growth'
    
    def _evaluate_debt(self, debt_ratio: float) -> str:
        """评估负债率"""
        if debt_ratio < 0.3:
            return 'low_risk'
        elif debt_ratio < 0.5:
            return 'moderate_risk'
        elif debt_ratio < 0.7:
            return 'high_risk'
        else:
            return 'very_high_risk'
    
    def _evaluate_liquidity(self, current_ratio: float) -> str:
        """评估流动性"""
        if current_ratio > 2:
            return 'excellent'
        elif current_ratio > 1.5:
            return 'good'
        elif current_ratio > 1:
            return 'adequate'
        else:
            return 'poor'
    
    def _calculate_fundamental_score(self, analysis: Dict[str, Any]) -> int:
        """计算基本面综合评分"""
        score = 50
        
        # PE评分
        pe_eval = analysis.get('valuation', {}).get('pe_evaluation')
        if pe_eval == 'undervalued':
            score += 10
        elif pe_eval == 'fair':
            score += 5
        elif pe_eval == 'overvalued':
            score -= 5
        elif pe_eval == 'highly_overvalued':
            score -= 10
        
        # ROE评分
        roe_eval = analysis.get('profitability', {}).get('roe_evaluation')
        if roe_eval == 'excellent':
            score += 15
        elif roe_eval == 'good':
            score += 10
        elif roe_eval == 'fair':
            score += 5
        
        # 成长性评分
        growth_eval = analysis.get('growth', {}).get('revenue_growth_evaluation')
        if growth_eval == 'high_growth':
            score += 15
        elif growth_eval == 'moderate_growth':
            score += 10
        elif growth_eval == 'slow_growth':
            score += 5
        elif growth_eval == 'negative_growth':
            score -= 10
        
        # 财务健康度评分
        debt_eval = analysis.get('financial_health', {}).get('debt_evaluation')
        if debt_eval == 'low_risk':
            score += 10
        elif debt_eval == 'moderate_risk':
            score += 5
        elif debt_eval == 'high_risk':
            score -= 5
        elif debt_eval == 'very_high_risk':
            score -= 10
        
        return max(0, min(100, score))
