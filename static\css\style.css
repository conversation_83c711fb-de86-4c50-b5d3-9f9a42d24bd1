/* 智能荐股系统样式 */

:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

body {
    font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
    background-color: #f5f5f5;
}

.section-title {
    color: var(--dark-color);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
}

/* 市场指数卡片 */
.market-index-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.market-index-card:hover {
    transform: translateY(-5px);
}

.market-index-card.positive {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.market-index-card.negative {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
}

.index-name {
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.index-price {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.index-change {
    font-size: 1rem;
    display: flex;
    align-items: center;
}

/* 推荐卡片 */
.recommendation-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.recommendation-card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.recommendation-card.sector {
    border-left-color: var(--success-color);
}

.recommendation-card.stock {
    border-left-color: var(--warning-color);
}

.card-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 15px;
}

.card-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--dark-color);
    margin: 0;
}

.recommendation-rank {
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
}

.score-badge {
    padding: 4px 8px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

.score-excellent {
    background-color: var(--success-color);
    color: white;
}

.score-good {
    background-color: var(--info-color);
    color: white;
}

.score-fair {
    background-color: var(--warning-color);
    color: white;
}

.score-poor {
    background-color: var(--danger-color);
    color: white;
}

/* 指标显示 */
.indicator-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.indicator-label {
    font-weight: 500;
    color: var(--dark-color);
}

.indicator-value {
    font-weight: bold;
}

.positive-value {
    color: var(--success-color);
}

.negative-value {
    color: var(--danger-color);
}

/* 新闻卡片 */
.news-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.news-card:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.news-title {
    font-size: 1rem;
    font-weight: bold;
    color: var(--dark-color);
    margin-bottom: 8px;
    line-height: 1.4;
}

.news-content {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 8px;
}

.news-meta {
    font-size: 0.8rem;
    color: #999;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 按钮样式 */
.btn-detail {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-detail:hover {
    background: linear-gradient(45deg, #0056b3, var(--primary-color));
    transform: translateY(-1px);
    color: white;
}

/* 加载动画 */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .market-index-card {
        margin-bottom: 15px;
    }
    
    .recommendation-card {
        margin-bottom: 15px;
        padding: 15px;
    }
    
    .index-price {
        font-size: 1.5rem;
    }
    
    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .recommendation-rank {
        margin-top: 10px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}

/* 工具提示 */
.tooltip-inner {
    background-color: var(--dark-color);
    color: white;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 0.9rem;
}

/* 风险等级标识 */
.risk-low {
    color: var(--success-color);
}

.risk-medium {
    color: var(--warning-color);
}

.risk-high {
    color: var(--danger-color);
}
