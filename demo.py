#!/usr/bin/env python3
"""
智能荐股小程序演示版本
展示系统功能和界面，使用模拟数据
"""

import json
import random
from datetime import datetime, timedelta
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse
import os

class StockRecommendationHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/':
            self.serve_html()
        elif self.path.startswith('/static/'):
            self.serve_static()
        elif self.path.startswith('/api/'):
            self.serve_api()
        else:
            self.send_error(404)
    
    def serve_html(self):
        """提供HTML页面"""
        html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能荐股小程序 - 演示版</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f5f5f5; }
        .section-title { color: #343a40; border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px; }
        .market-index-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; }
        .market-index-card.positive { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
        .market-index-card.negative { background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%); }
        .recommendation-card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-left: 4px solid #007bff; }
        .recommendation-card.sector { border-left-color: #28a745; }
        .recommendation-card.stock { border-left-color: #ffc107; }
        .score-excellent { background-color: #28a745; color: white; padding: 4px 8px; border-radius: 15px; font-size: 0.8rem; }
        .score-good { background-color: #17a2b8; color: white; padding: 4px 8px; border-radius: 15px; font-size: 0.8rem; }
        .score-fair { background-color: #ffc107; color: white; padding: 4px 8px; border-radius: 15px; font-size: 0.8rem; }
        .positive-value { color: #28a745; }
        .negative-value { color: #dc3545; }
        .news-card { background: white; border-radius: 8px; padding: 15px; margin-bottom: 15px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#"><i class="fas fa-chart-line me-2"></i>智能荐股系统</a>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 市场概览 -->
        <section class="mb-5">
            <h2 class="section-title"><i class="fas fa-globe me-2"></i>市场概览</h2>
            <div class="row" id="market-indices">
                <div class="col-12 text-center">
                    <div class="spinner-border text-primary" role="status"></div>
                </div>
            </div>
        </section>

        <!-- 板块推荐 -->
        <section class="mb-5">
            <h2 class="section-title"><i class="fas fa-layer-group me-2"></i>热门板块推荐</h2>
            <div class="row" id="sector-list">
                <div class="col-12 text-center">
                    <div class="spinner-border text-success" role="status"></div>
                </div>
            </div>
        </section>

        <!-- 个股推荐 -->
        <section class="mb-5">
            <h2 class="section-title"><i class="fas fa-star me-2"></i>精选个股推荐</h2>
            <div class="row" id="stock-list">
                <div class="col-12 text-center">
                    <div class="spinner-border text-warning" role="status"></div>
                </div>
            </div>
        </section>

        <!-- 市场资讯 -->
        <section class="mb-5">
            <h2 class="section-title"><i class="fas fa-newspaper me-2"></i>市场资讯</h2>
            <div class="row" id="news-list">
                <div class="col-12 text-center">
                    <div class="spinner-border text-info" role="status"></div>
                </div>
            </div>
        </section>
    </div>

    <footer class="bg-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">© 2024 智能荐股系统 - 基于Tushare数据</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0"><small class="text-muted">演示版本 - 使用模拟数据</small></p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 加载数据
        async function loadData() {
            try {
                // 加载市场概览
                const marketResponse = await fetch('/api/market_overview');
                const marketData = await marketResponse.json();
                renderMarketOverview(marketData.data);

                // 加载板块推荐
                const sectorResponse = await fetch('/api/recommendations?type=sector');
                const sectorData = await sectorResponse.json();
                renderSectorRecommendations(sectorData.data);

                // 加载个股推荐
                const stockResponse = await fetch('/api/recommendations?type=stock');
                const stockData = await stockResponse.json();
                renderStockRecommendations(stockData.data);

                // 加载新闻
                const newsResponse = await fetch('/api/news');
                const newsData = await newsResponse.json();
                renderNews(newsData.data);
            } catch (error) {
                console.error('加载数据失败:', error);
            }
        }

        function renderMarketOverview(data) {
            const container = document.getElementById('market-indices');
            let html = '';
            for (const [indexCode, indexData] of Object.entries(data)) {
                const changeClass = indexData.change >= 0 ? 'positive' : 'negative';
                const changeIcon = indexData.change >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';
                const changeSign = indexData.change >= 0 ? '+' : '';
                html += `
                    <div class="col-md-4 mb-3">
                        <div class="market-index-card ${changeClass}">
                            <div style="font-size: 1.1rem; font-weight: bold;">${indexData.name}</div>
                            <div style="font-size: 1.8rem; font-weight: bold;">${indexData.close.toFixed(2)}</div>
                            <div><i class="fas ${changeIcon} me-2"></i>${changeSign}${indexData.change.toFixed(2)} (${changeSign}${indexData.change_pct.toFixed(2)}%)</div>
                        </div>
                    </div>
                `;
            }
            container.innerHTML = html;
        }

        function renderSectorRecommendations(sectors) {
            const container = document.getElementById('sector-list');
            let html = '';
            sectors.forEach((sector, index) => {
                const scoreClass = sector.recommendation_score >= 80 ? 'score-excellent' : 
                                 sector.recommendation_score >= 70 ? 'score-good' : 'score-fair';
                html += `
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="recommendation-card sector">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <div style="font-size: 1.2rem; font-weight: bold;">${sector.sector_name}</div>
                                    <small class="text-muted">成分股: ${sector.stock_count}只</small>
                                </div>
                                <div style="background: #007bff; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; font-weight: bold;">${sector.rank}</div>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>推荐评分</span>
                                <span class="${scoreClass}">${sector.recommendation_score.toFixed(1)}分</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>平均涨幅</span>
                                <span class="${sector.avg_change_pct >= 0 ? 'positive-value' : 'negative-value'}">${sector.avg_change_pct >= 0 ? '+' : ''}${sector.avg_change_pct}%</span>
                            </div>
                            <div class="mt-3">
                                <small class="text-muted"><i class="fas fa-lightbulb me-1"></i>${sector.recommendation_reason}</small>
                            </div>
                        </div>
                    </div>
                `;
            });
            container.innerHTML = html;
        }

        function renderStockRecommendations(stocks) {
            const container = document.getElementById('stock-list');
            let html = '';
            stocks.forEach((stock, index) => {
                const scoreClass = stock.recommendation_score >= 80 ? 'score-excellent' : 
                                 stock.recommendation_score >= 70 ? 'score-good' : 'score-fair';
                const priceChangeClass = stock.price_change_pct >= 0 ? 'positive-value' : 'negative-value';
                html += `
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="recommendation-card stock">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <div style="font-size: 1.2rem; font-weight: bold;">${stock.name}</div>
                                    <small class="text-muted">${stock.ts_code} | ${stock.industry}</small>
                                </div>
                                <div style="background: #ffc107; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; font-weight: bold;">${stock.rank}</div>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>当前价格</span>
                                <span>¥${stock.current_price.toFixed(2)}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>涨跌幅</span>
                                <span class="${priceChangeClass}">${stock.price_change_pct >= 0 ? '+' : ''}${stock.price_change_pct.toFixed(2)}%</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>推荐评分</span>
                                <span class="${scoreClass}">${stock.recommendation_score.toFixed(1)}分</span>
                            </div>
                            <div class="mt-3">
                                <small class="text-muted"><i class="fas fa-lightbulb me-1"></i>${stock.recommendation_reason}</small>
                            </div>
                        </div>
                    </div>
                `;
            });
            container.innerHTML = html;
        }

        function renderNews(news) {
            const container = document.getElementById('news-list');
            let html = '';
            news.forEach((item, index) => {
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="news-card">
                            <div style="font-size: 1rem; font-weight: bold; margin-bottom: 8px;">${item.title}</div>
                            <div style="color: #666; font-size: 0.9rem; margin-bottom: 8px;">${item.content}</div>
                            <div style="font-size: 0.8rem; color: #999; display: flex; justify-content: space-between;">
                                <span><i class="fas fa-clock me-1"></i>${item.datetime}</span>
                                <span><i class="fas fa-tag me-1"></i>${item.source}</span>
                            </div>
                        </div>
                    </div>
                `;
            });
            container.innerHTML = html;
        }

        // 页面加载完成后加载数据
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def serve_api(self):
        """提供API服务"""
        if self.path == '/api/market_overview':
            data = self.generate_mock_market_data()
        elif self.path.startswith('/api/recommendations'):
            query = urllib.parse.urlparse(self.path).query
            params = urllib.parse.parse_qs(query)
            rec_type = params.get('type', ['sector'])[0]
            
            if rec_type == 'sector':
                data = self.generate_mock_sector_recommendations()
            else:
                data = self.generate_mock_stock_recommendations()
        elif self.path == '/api/news':
            data = self.generate_mock_news()
        else:
            self.send_error(404)
            return
        
        response = {
            'status': 'success',
            'data': data,
            'timestamp': datetime.now().isoformat()
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
    
    def generate_mock_market_data(self):
        """生成模拟市场数据"""
        return {
            '000001.SH': {
                'name': '上证指数',
                'close': 3200.50 + random.uniform(-50, 50),
                'change': random.uniform(-30, 30),
                'change_pct': random.uniform(-1.5, 1.5),
                'volume': random.uniform(200000000, 500000000)
            },
            '399001.SZ': {
                'name': '深证成指',
                'close': 12500.30 + random.uniform(-200, 200),
                'change': random.uniform(-100, 100),
                'change_pct': random.uniform(-2, 2),
                'volume': random.uniform(150000000, 400000000)
            },
            '399006.SZ': {
                'name': '创业板指',
                'close': 2800.80 + random.uniform(-100, 100),
                'change': random.uniform(-50, 50),
                'change_pct': random.uniform(-2.5, 2.5),
                'volume': random.uniform(100000000, 300000000)
            }
        }
    
    def generate_mock_sector_recommendations(self):
        """生成模拟板块推荐"""
        sectors = [
            {'name': '人工智能', 'reason': '政策利好，技术突破'},
            {'name': '新能源汽车', 'reason': '销量增长，产业链完善'},
            {'name': '医疗器械', 'reason': '需求稳定，创新驱动'},
            {'name': '半导体', 'reason': '国产替代，技术升级'},
            {'name': '光伏产业', 'reason': '成本下降，需求旺盛'},
            {'name': '5G通信', 'reason': '基建完善，应用拓展'},
            {'name': '生物医药', 'reason': '研发进展，政策支持'},
            {'name': '新材料', 'reason': '下游需求，技术进步'}
        ]
        
        recommendations = []
        for i, sector in enumerate(sectors):
            recommendations.append({
                'rank': i + 1,
                'sector_code': f'SECTOR_{i+1:03d}',
                'sector_name': sector['name'],
                'stock_count': random.randint(20, 100),
                'avg_change_pct': round(random.uniform(-3, 8), 2),
                'avg_technical_score': round(random.uniform(55, 85), 1),
                'recommendation_score': round(random.uniform(65, 90), 1),
                'recommendation_reason': sector['reason']
            })
        
        return sorted(recommendations, key=lambda x: x['recommendation_score'], reverse=True)
    
    def generate_mock_stock_recommendations(self):
        """生成模拟个股推荐"""
        stocks = [
            {'name': '比亚迪', 'code': '002594.SZ', 'industry': '汽车制造'},
            {'name': '宁德时代', 'code': '300750.SZ', 'industry': '电池制造'},
            {'name': '贵州茅台', 'code': '600519.SH', 'industry': '白酒制造'},
            {'name': '中国平安', 'code': '601318.SH', 'industry': '保险'},
            {'name': '招商银行', 'code': '600036.SH', 'industry': '银行'},
            {'name': '五粮液', 'code': '000858.SZ', 'industry': '白酒制造'},
            {'name': '美的集团', 'code': '000333.SZ', 'industry': '家电制造'},
            {'name': '隆基绿能', 'code': '601012.SH', 'industry': '光伏制造'},
            {'name': '药明康德', 'code': '603259.SH', 'industry': '医药研发'},
            {'name': '海康威视', 'code': '002415.SZ', 'industry': '安防设备'}
        ]
        
        recommendations = []
        for i, stock in enumerate(stocks):
            recommendations.append({
                'rank': i + 1,
                'ts_code': stock['code'],
                'name': stock['name'],
                'industry': stock['industry'],
                'current_price': round(random.uniform(10, 200), 2),
                'price_change_pct': round(random.uniform(-5, 8), 2),
                'technical_score': round(random.uniform(60, 90), 1),
                'fundamental_score': round(random.uniform(55, 85), 1),
                'recommendation_score': round(random.uniform(70, 95), 1),
                'recommendation_reason': f'{stock["industry"]}龙头，基本面良好，技术面强势'
            })
        
        return sorted(recommendations, key=lambda x: x['recommendation_score'], reverse=True)
    
    def generate_mock_news(self):
        """生成模拟新闻"""
        news_titles = [
            "A股三大指数集体收涨，新能源板块表现强势",
            "央行降准释放流动性，市场情绪回暖",
            "科技股领涨，人工智能概念持续活跃",
            "消费板块回调，白酒股分化明显",
            "新能源汽车销量创新高，产业链受益",
            "医药板块震荡上行，创新药获资金关注"
        ]
        
        news_list = []
        for i, title in enumerate(news_titles):
            news_list.append({
                'title': title,
                'content': f'{title}。市场分析认为，当前宏观环境有利于相关板块发展，投资者可关注龙头企业的投资机会。',
                'datetime': (datetime.now() - timedelta(hours=i)).strftime('%Y-%m-%d %H:%M'),
                'source': 'sina'
            })
        
        return news_list

def main():
    """主函数"""
    port = 8000
    server = HTTPServer(('localhost', port), StockRecommendationHandler)
    
    print("🚀 启动智能荐股小程序演示版...")
    print(f"🌐 访问地址: http://localhost:{port}")
    print("📊 使用模拟数据进行演示")
    print("=" * 50)
    print("按 Ctrl+C 停止服务")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
        server.shutdown()

if __name__ == '__main__':
    main()
