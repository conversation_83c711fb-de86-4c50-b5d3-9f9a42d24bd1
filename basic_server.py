import http.server
import socketserver
import os

PORT = 8888

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

os.chdir(r'C:\Users\<USER>\Desktop\stock_app')

with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
    print(f"服务器启动在端口 {PORT}")
    print(f"访问地址: http://localhost:{PORT}/corrected_stock_demo.html")
    httpd.serve_forever()
