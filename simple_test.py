#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("Python 测试开始...")

try:
    import sys
    print(f"Python 版本: {sys.version}")
    
    import http.server
    import socketserver
    print("HTTP 服务器模块导入成功")
    
    import json
    print("JSON 模块导入成功")
    
    import logging
    print("日志模块导入成功")
    
    print("所有基础模块导入成功!")
    
    # 测试简单的HTTP服务器
    PORT = 8888
    
    class TestHandler(http.server.SimpleHTTPRequestHandler):
        def do_GET(self):
            if self.path == '/':
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <title>测试服务器</title>
                </head>
                <body>
                    <h1>🎉 服务器运行正常!</h1>
                    <p>半导体板块测试数据:</p>
                    <ul>
                        <li>指数: 2831.81</li>
                        <li>涨跌幅: -1.15%</li>
                        <li>数据源: 最新准确数据</li>
                    </ul>
                </body>
                </html>
                """
                self.wfile.write(html.encode('utf-8'))
            else:
                self.send_response(404)
                self.end_headers()
    
    print(f"启动测试服务器在端口 {PORT}...")
    with socketserver.TCPServer(("", PORT), TestHandler) as httpd:
        print(f"✅ 测试服务器启动成功!")
        print(f"🌐 访问地址: http://localhost:{PORT}")
        print("按 Ctrl+C 停止服务")
        httpd.serve_forever()
        
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
