@echo off
echo ========================================
echo Python环境诊断工具
echo ========================================
echo.

echo 1. 检查Python是否安装...
python --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ Python未找到或未正确安装
    echo.
    echo 尝试使用py命令...
    py --version 2>nul
    if %errorlevel% neq 0 (
        echo ❌ py命令也未找到
        echo.
        echo 🔧 解决方案:
        echo 1. 请安装Python 3.x版本
        echo 2. 确保Python已添加到系统PATH
        echo 3. 重启命令提示符
        goto :end
    ) else (
        echo ✅ 找到py命令
        set PYTHON_CMD=py
    )
) else (
    echo ✅ 找到python命令
    set PYTHON_CMD=python
)

echo.
echo 2. 检查Python版本...
%PYTHON_CMD% --version

echo.
echo 3. 检查当前目录...
echo 当前目录: %CD%

echo.
echo 4. 检查端口8888是否被占用...
netstat -ano | findstr :8888
if %errorlevel% equ 0 (
    echo ⚠️ 端口8888已被占用
) else (
    echo ✅ 端口8888可用
)

echo.
echo 5. 尝试启动简单HTTP服务器...
echo 使用命令: %PYTHON_CMD% -m http.server 8888
echo.
echo 如果成功，请访问: http://localhost:8888/final_accurate_stock_app.html
echo.
echo 按任意键开始启动服务器...
pause >nul

%PYTHON_CMD% -m http.server 8888

:end
echo.
echo 诊断完成
pause
