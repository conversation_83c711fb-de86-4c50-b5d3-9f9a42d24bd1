#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版API数据
"""

import requests
import json

try:
    print("正在测试增强版API...")
    r = requests.get('http://localhost:8888/api/data', timeout=10)
    print(f"HTTP状态码: {r.status_code}")
    
    data = r.json()
    
    print("API状态:", data['status'])
    print("服务器时间:", data.get('server_time', '未知'))
    
    # 测试市场数据
    market = data['data']['market']
    print(f"\n📊 市场数据 (共{len(market)}个指数):")
    for code, info in market.items():
        change_symbol = "🔴" if info['change'] >= 0 else "🟢"
        print(f"{change_symbol} {info['name']}: {info['close']:.2f}点 ({info['change_pct']:+.2f}%)")
    
    # 测试板块数据
    if 'sectors' in data['data']:
        sectors = data['data']['sectors']
        print(f"\n🔥 板块推荐 (共{len(sectors)}个板块):")
        for sector in sectors[:5]:  # 显示前5个
            print(f"📈 {sector['name']}: {sector['score']}分 ({sector['recommendation_level']}) - {sector['current_change_pct']:+.2f}%")
            print(f"   推荐理由: {sector['reason'][:50]}...")
    else:
        print("\n❌ 未找到板块数据")
    
    print(f"\n✅ 增强版API测试成功!")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
