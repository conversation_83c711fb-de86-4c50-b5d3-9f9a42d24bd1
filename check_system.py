#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时荐股系统状态检查工具
"""

import requests
import json
import time
from datetime import datetime

def check_system_status():
    """检查系统状态"""
    print("🔍 正在检查实时荐股系统状态...")
    print("=" * 50)
    
    # 检查服务是否运行
    try:
        response = requests.get('http://localhost:8080', timeout=5)
        if response.status_code == 200:
            print("✅ Web服务运行正常")
            print(f"   状态码: {response.status_code}")
            print(f"   响应时间: {response.elapsed.total_seconds():.2f}秒")
        else:
            print(f"❌ Web服务异常，状态码: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务，请确认服务是否启动")
        print("   启动命令: python simple_real_time.py")
        return False
    except requests.exceptions.Timeout:
        print("❌ 服务响应超时")
        return False
    except Exception as e:
        print(f"❌ 检查服务时出错: {e}")
        return False
    
    # 检查API接口
    try:
        api_response = requests.get('http://localhost:8080/api/data', timeout=10)
        if api_response.status_code == 200:
            print("✅ API接口运行正常")
            
            # 解析数据
            data = api_response.json()
            if data.get('status') == 'success':
                print("✅ 数据获取成功")
                
                # 检查市场数据
                market_data = data.get('data', {}).get('market', {})
                if market_data:
                    print(f"✅ 市场数据: {len(market_data)}个指数")
                    for code, info in market_data.items():
                        print(f"   {info['name']}: {info['close']:.2f} ({info['change_pct']:+.2f}%)")
                else:
                    print("⚠️  市场数据为空")
                
                # 检查股票数据
                stock_data = data.get('data', {}).get('stocks', [])
                if stock_data:
                    print(f"✅ 股票推荐: {len(stock_data)}只股票")
                    for i, stock in enumerate(stock_data[:3]):  # 显示前3只
                        print(f"   {i+1}. {stock['name']}: {stock['score']}分 ({stock['change_pct']:+.2f}%)")
                else:
                    print("⚠️  股票推荐数据为空")
                
                # 检查新闻数据
                news_data = data.get('data', {}).get('news', [])
                if news_data:
                    print(f"✅ 市场资讯: {len(news_data)}条新闻")
                else:
                    print("⚠️  新闻数据为空")
                
                # 检查更新时间
                last_update = data.get('data', {}).get('last_update')
                if last_update:
                    print(f"✅ 最后更新: {last_update}")
                else:
                    print("⚠️  无更新时间信息")
                    
            else:
                print("❌ API返回错误状态")
                return False
        else:
            print(f"❌ API接口异常，状态码: {api_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 检查API时出错: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 系统状态检查完成！")
    print("🌐 访问地址: http://localhost:8080")
    print("📊 系统正常运行，数据实时更新中...")
    
    return True

def monitor_system(duration=300):
    """监控系统运行状态"""
    print(f"\n🔄 开始监控系统状态 ({duration}秒)...")
    print("按 Ctrl+C 停止监控")
    print("-" * 50)
    
    start_time = time.time()
    check_count = 0
    success_count = 0
    
    try:
        while time.time() - start_time < duration:
            check_count += 1
            current_time = datetime.now().strftime('%H:%M:%S')
            
            try:
                response = requests.get('http://localhost:8080/api/data', timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('status') == 'success':
                        success_count += 1
                        market_count = len(data.get('data', {}).get('market', {}))
                        stock_count = len(data.get('data', {}).get('stocks', []))
                        print(f"[{current_time}] ✅ 正常 - 市场:{market_count} 股票:{stock_count} 响应:{response.elapsed.total_seconds():.2f}s")
                    else:
                        print(f"[{current_time}] ❌ 数据错误")
                else:
                    print(f"[{current_time}] ❌ HTTP {response.status_code}")
            except Exception as e:
                print(f"[{current_time}] ❌ 连接失败: {str(e)[:30]}...")
            
            time.sleep(30)  # 每30秒检查一次
        
        # 统计结果
        success_rate = (success_count / check_count) * 100 if check_count > 0 else 0
        print("-" * 50)
        print(f"📊 监控统计:")
        print(f"   总检查次数: {check_count}")
        print(f"   成功次数: {success_count}")
        print(f"   成功率: {success_rate:.1f}%")
        
    except KeyboardInterrupt:
        print("\n👋 监控已停止")

def test_data_freshness():
    """测试数据新鲜度"""
    print("\n🕐 测试数据更新频率...")
    
    try:
        # 第一次获取数据
        response1 = requests.get('http://localhost:8080/api/data', timeout=5)
        data1 = response1.json()
        time1 = data1.get('data', {}).get('last_update')
        
        print(f"第一次获取: {time1}")
        print("等待35秒后再次获取...")
        
        # 等待35秒
        time.sleep(35)
        
        # 第二次获取数据
        response2 = requests.get('http://localhost:8080/api/data', timeout=5)
        data2 = response2.json()
        time2 = data2.get('data', {}).get('last_update')
        
        print(f"第二次获取: {time2}")
        
        if time1 != time2:
            print("✅ 数据已更新，系统正常运行")
        else:
            print("⚠️  数据未更新，可能存在问题")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🚀 实时荐股系统检查工具")
    print("=" * 50)
    
    # 基础状态检查
    if not check_system_status():
        print("\n❌ 系统检查失败，请检查服务是否正常启动")
        return
    
    # 询问用户是否进行进一步测试
    print("\n🔧 可选测试项目:")
    print("1. 监控系统运行状态 (5分钟)")
    print("2. 测试数据更新频率")
    print("3. 退出")
    
    try:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == '1':
            monitor_system(300)  # 监控5分钟
        elif choice == '2':
            test_data_freshness()
        elif choice == '3':
            print("👋 退出检查工具")
        else:
            print("⚠️  无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 检查工具已退出")

if __name__ == '__main__':
    main()
