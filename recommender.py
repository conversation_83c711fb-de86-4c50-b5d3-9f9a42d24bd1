"""
荐股策略模块
基于技术分析和基本面分析结果，提供智能股票推荐
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Tuple
import logging
from datetime import datetime, timedelta
from collections import defaultdict

from config import Config
from data_fetcher import DataFetcher
from analyzer import StockAnalyzer

logger = logging.getLogger(__name__)

class StockRecommender:
    """股票推荐器"""
    
    def __init__(self):
        """初始化推荐器"""
        self.data_fetcher = DataFetcher()
        self.analyzer = StockAnalyzer()
        self.recommendation_params = Config.RECOMMENDATION_PARAMS
        self.risk_params = Config.RISK_PARAMS
        
        # 缓存
        self._cache = {}
        self._cache_timeout = 1800  # 30分钟缓存
    
    def _get_cache_key(self, func_name: str, **kwargs) -> str:
        """生成缓存键"""
        key_parts = [func_name]
        for k, v in sorted(kwargs.items()):
            key_parts.append(f"{k}={v}")
        return "|".join(key_parts)
    
    def _get_from_cache(self, cache_key: str) -> Any:
        """从缓存获取数据"""
        if cache_key in self._cache:
            data, timestamp = self._cache[cache_key]
            if (datetime.now() - timestamp).seconds < self._cache_timeout:
                return data
            else:
                del self._cache[cache_key]
        return None
    
    def _set_cache(self, cache_key: str, data: Any):
        """设置缓存"""
        self._cache[cache_key] = (data, datetime.now())
    
    def get_sector_recommendations(self) -> List[Dict[str, Any]]:
        """获取板块推荐"""
        cache_key = self._get_cache_key('sector_recommendations')
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data
        
        try:
            logger.info("开始分析板块推荐...")
            
            # 获取主要指数数据进行市场分析
            market_analysis = self._analyze_market_trend()
            
            # 获取板块数据
            sectors = self.data_fetcher.get_sector_data()
            
            sector_recommendations = []
            
            for sector in sectors[:20]:  # 分析前20个板块
                try:
                    sector_analysis = self._analyze_sector(sector, market_analysis)
                    if sector_analysis:
                        sector_recommendations.append(sector_analysis)
                except Exception as e:
                    logger.error(f"分析板块 {sector['name']} 失败: {str(e)}")
                    continue
            
            # 按推荐度排序
            sector_recommendations.sort(key=lambda x: x['recommendation_score'], reverse=True)
            
            # 添加推荐理由
            for i, sector in enumerate(sector_recommendations[:10]):
                sector['rank'] = i + 1
                sector['recommendation_reason'] = self._generate_sector_reason(sector)
            
            result = sector_recommendations[:10]
            self._set_cache(cache_key, result)
            
            logger.info(f"完成板块分析，推荐 {len(result)} 个板块")
            return result
            
        except Exception as e:
            logger.error(f"获取板块推荐失败: {str(e)}")
            return []
    
    def get_stock_recommendations(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取个股推荐"""
        cache_key = self._get_cache_key('stock_recommendations', limit=limit)
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data
        
        try:
            logger.info("开始分析个股推荐...")
            
            # 获取股票列表
            stock_list = self.data_fetcher.get_stock_list()
            
            if stock_list.empty:
                return []
            
            # 过滤股票（排除ST、退市等）
            filtered_stocks = self._filter_stocks(stock_list)
            
            stock_recommendations = []
            analyzed_count = 0
            max_analyze = min(200, len(filtered_stocks))  # 最多分析200只股票
            
            for _, stock in filtered_stocks.head(max_analyze).iterrows():
                try:
                    stock_analysis = self._analyze_stock_for_recommendation(stock)
                    if stock_analysis and stock_analysis['recommendation_score'] > 60:
                        stock_recommendations.append(stock_analysis)
                    
                    analyzed_count += 1
                    if analyzed_count % 50 == 0:
                        logger.info(f"已分析 {analyzed_count} 只股票...")
                        
                except Exception as e:
                    logger.error(f"分析股票 {stock['ts_code']} 失败: {str(e)}")
                    continue
            
            # 按推荐度排序
            stock_recommendations.sort(key=lambda x: x['recommendation_score'], reverse=True)
            
            # 添加推荐理由和风险评估
            for i, stock in enumerate(stock_recommendations[:limit]):
                stock['rank'] = i + 1
                stock['recommendation_reason'] = self._generate_stock_reason(stock)
                stock['risk_assessment'] = self._assess_stock_risk(stock)
            
            result = stock_recommendations[:limit]
            self._set_cache(cache_key, result)
            
            logger.info(f"完成个股分析，推荐 {len(result)} 只股票")
            return result
            
        except Exception as e:
            logger.error(f"获取个股推荐失败: {str(e)}")
            return []
    
    def _analyze_market_trend(self) -> Dict[str, Any]:
        """分析市场趋势"""
        try:
            indices = ['000001.SH', '399001.SZ', '399006.SZ']
            market_data = {}
            
            for index_code in indices:
                index_data = self.data_fetcher.get_index_daily(index_code, days=30)
                if not index_data.empty:
                    technical_analysis = self.analyzer.technical_analysis(index_data)
                    market_data[index_code] = technical_analysis
            
            # 综合市场趋势
            overall_trend = self._get_overall_market_trend(market_data)
            
            return {
                'indices_analysis': market_data,
                'overall_trend': overall_trend,
                'market_sentiment': self._get_market_sentiment(overall_trend)
            }
            
        except Exception as e:
            logger.error(f"市场趋势分析失败: {str(e)}")
            return {}
    
    def _get_overall_market_trend(self, market_data: Dict[str, Any]) -> str:
        """获取整体市场趋势"""
        bullish_count = 0
        total_count = 0
        
        for index_code, analysis in market_data.items():
            if 'trend_analysis' in analysis:
                trend = analysis['trend_analysis'].get('overall_trend', 'neutral')
                total_count += 1
                if trend == 'bullish':
                    bullish_count += 1
        
        if total_count == 0:
            return 'neutral'
        
        bullish_ratio = bullish_count / total_count
        if bullish_ratio >= 0.67:
            return 'bullish'
        elif bullish_ratio <= 0.33:
            return 'bearish'
        else:
            return 'neutral'
    
    def _get_market_sentiment(self, overall_trend: str) -> str:
        """获取市场情绪"""
        sentiment_map = {
            'bullish': 'optimistic',
            'bearish': 'pessimistic',
            'neutral': 'cautious'
        }
        return sentiment_map.get(overall_trend, 'cautious')
    
    def _analyze_sector(self, sector: Dict[str, Any], market_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """分析单个板块"""
        try:
            sector_stocks = sector.get('stocks', [])[:5]  # 分析前5只成分股
            
            if not sector_stocks:
                return None
            
            stock_performances = []
            
            for stock_code in sector_stocks:
                try:
                    stock_data = self.data_fetcher.get_stock_daily(stock_code, days=30)
                    if not stock_data.empty:
                        # 计算涨跌幅
                        latest_price = stock_data['close'].iloc[-1]
                        prev_price = stock_data['close'].iloc[0]
                        change_pct = (latest_price - prev_price) / prev_price * 100
                        
                        # 技术分析
                        technical = self.analyzer.technical_analysis(stock_data)
                        
                        stock_performances.append({
                            'ts_code': stock_code,
                            'change_pct': change_pct,
                            'technical_score': technical.get('technical_score', {}).get('score', 50)
                        })
                except Exception:
                    continue
            
            if not stock_performances:
                return None
            
            # 计算板块平均表现
            avg_change = np.mean([s['change_pct'] for s in stock_performances])
            avg_technical_score = np.mean([s['technical_score'] for s in stock_performances])
            
            # 计算推荐分数
            recommendation_score = self._calculate_sector_score(
                avg_change, avg_technical_score, market_analysis
            )
            
            return {
                'sector_code': sector['code'],
                'sector_name': sector['name'],
                'stock_count': sector['stock_count'],
                'avg_change_pct': round(avg_change, 2),
                'avg_technical_score': round(avg_technical_score, 1),
                'recommendation_score': round(recommendation_score, 1),
                'sample_stocks': stock_performances
            }
            
        except Exception as e:
            logger.error(f"分析板块失败: {str(e)}")
            return None
    
    def _calculate_sector_score(self, avg_change: float, avg_technical_score: float, 
                              market_analysis: Dict[str, Any]) -> float:
        """计算板块推荐分数"""
        score = 50.0
        
        # 涨跌幅评分
        if avg_change > 5:
            score += 20
        elif avg_change > 2:
            score += 10
        elif avg_change > 0:
            score += 5
        elif avg_change < -5:
            score -= 20
        elif avg_change < -2:
            score -= 10
        
        # 技术分析评分
        score += (avg_technical_score - 50) * 0.5
        
        # 市场环境调整
        market_sentiment = market_analysis.get('market_sentiment', 'cautious')
        if market_sentiment == 'optimistic':
            score += 5
        elif market_sentiment == 'pessimistic':
            score -= 5
        
        return max(0, min(100, score))
    
    def _filter_stocks(self, stock_list: pd.DataFrame) -> pd.DataFrame:
        """过滤股票"""
        # 排除ST股票、退市股票等
        filtered = stock_list[
            (~stock_list['name'].str.contains('ST', na=False)) &
            (~stock_list['name'].str.contains('退', na=False)) &
            (~stock_list['name'].str.contains('*', na=False))
        ]
        
        # 按上市时间排序，优先选择上市较久的股票
        filtered = filtered.sort_values('list_date')
        
        return filtered
    
    def _analyze_stock_for_recommendation(self, stock: pd.Series) -> Dict[str, Any]:
        """分析单只股票用于推荐"""
        try:
            ts_code = stock['ts_code']
            
            # 获取股票数据
            stock_data = self.data_fetcher.get_stock_daily(ts_code, days=60)
            if stock_data.empty:
                return None
            
            # 获取基本信息
            basic_info = self.data_fetcher.get_stock_basic_info(ts_code)
            financial_data = self.data_fetcher.get_financial_data(ts_code)
            
            # 技术分析
            technical_analysis = self.analyzer.technical_analysis(stock_data)
            
            # 基本面分析
            fundamental_analysis = self.analyzer.fundamental_analysis(basic_info, financial_data)
            
            # 过滤条件检查
            if not self._meets_recommendation_criteria(basic_info, fundamental_analysis):
                return None
            
            # 计算综合推荐分数
            recommendation_score = self._calculate_stock_recommendation_score(
                technical_analysis, fundamental_analysis, basic_info
            )
            
            return {
                'ts_code': ts_code,
                'name': stock['name'],
                'industry': stock.get('industry', ''),
                'area': stock.get('area', ''),
                'current_price': technical_analysis.get('price_info', {}).get('current_price', 0),
                'price_change_pct': technical_analysis.get('price_info', {}).get('price_change_pct', 0),
                'technical_score': technical_analysis.get('technical_score', {}).get('score', 50),
                'fundamental_score': fundamental_analysis.get('fundamental_score', 50),
                'recommendation_score': recommendation_score,
                'market_cap': basic_info.get('total_mv', 0),
                'pe_ratio': basic_info.get('pe', 0),
                'pb_ratio': basic_info.get('pb', 0)
            }
            
        except Exception as e:
            logger.error(f"分析股票 {stock['ts_code']} 推荐失败: {str(e)}")
            return None
    
    def _meets_recommendation_criteria(self, basic_info: Dict[str, Any], 
                                     fundamental_analysis: Dict[str, Any]) -> bool:
        """检查是否满足推荐条件"""
        try:
            # 市值过滤
            market_cap = basic_info.get('total_mv', 0)
            if market_cap and market_cap < self.recommendation_params['min_market_cap'] * 10000:  # 转换为万元
                return False
            
            # 市盈率过滤
            pe_ratio = basic_info.get('pe', 0)
            if pe_ratio and (pe_ratio < 0 or pe_ratio > self.recommendation_params['max_pe_ratio']):
                return False
            
            # ROE过滤
            roe = fundamental_analysis.get('profitability', {}).get('roe', 0)
            if roe and roe < self.recommendation_params['min_roe']:
                return False
            
            return True
            
        except Exception:
            return True  # 如果检查失败，默认通过
    
    def _calculate_stock_recommendation_score(self, technical_analysis: Dict[str, Any],
                                            fundamental_analysis: Dict[str, Any],
                                            basic_info: Dict[str, Any]) -> float:
        """计算股票推荐分数"""
        try:
            # 基础分数
            score = 50.0
            
            # 技术分析权重 40%
            technical_score = technical_analysis.get('technical_score', {}).get('score', 50)
            score += (technical_score - 50) * 0.4
            
            # 基本面分析权重 40%
            fundamental_score = fundamental_analysis.get('fundamental_score', 50)
            score += (fundamental_score - 50) * 0.4
            
            # 估值调整权重 20%
            pe_ratio = basic_info.get('pe', 0)
            if pe_ratio and pe_ratio > 0:
                if pe_ratio < 15:
                    score += 10
                elif pe_ratio < 25:
                    score += 5
                elif pe_ratio > 40:
                    score -= 10
            
            pb_ratio = basic_info.get('pb', 0)
            if pb_ratio and pb_ratio > 0:
                if pb_ratio < 1.5:
                    score += 5
                elif pb_ratio > 3:
                    score -= 5
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算推荐分数失败: {str(e)}")
            return 50.0
    
    def _generate_sector_reason(self, sector: Dict[str, Any]) -> str:
        """生成板块推荐理由"""
        reasons = []
        
        if sector['avg_change_pct'] > 3:
            reasons.append("近期涨幅较大，表现强势")
        elif sector['avg_change_pct'] > 0:
            reasons.append("近期表现稳定上涨")
        
        if sector['avg_technical_score'] > 65:
            reasons.append("技术指标表现良好")
        elif sector['avg_technical_score'] > 55:
            reasons.append("技术面偏向积极")
        
        if sector['recommendation_score'] > 75:
            reasons.append("综合评分优秀")
        elif sector['recommendation_score'] > 65:
            reasons.append("综合评分良好")
        
        return "；".join(reasons) if reasons else "综合分析结果"
    
    def _generate_stock_reason(self, stock: Dict[str, Any]) -> str:
        """生成个股推荐理由"""
        reasons = []
        
        if stock['technical_score'] > 70:
            reasons.append("技术面强势")
        elif stock['technical_score'] > 60:
            reasons.append("技术面良好")
        
        if stock['fundamental_score'] > 70:
            reasons.append("基本面优秀")
        elif stock['fundamental_score'] > 60:
            reasons.append("基本面良好")
        
        if stock.get('pe_ratio', 0) > 0 and stock['pe_ratio'] < 20:
            reasons.append("估值合理")
        
        if stock['price_change_pct'] > 2:
            reasons.append("近期表现强势")
        
        return "；".join(reasons) if reasons else "综合分析推荐"
    
    def _assess_stock_risk(self, stock: Dict[str, Any]) -> Dict[str, Any]:
        """评估股票风险"""
        risk_level = "中等"
        risk_factors = []
        
        # 估值风险
        pe_ratio = stock.get('pe_ratio', 0)
        if pe_ratio > 50:
            risk_factors.append("估值偏高")
            risk_level = "较高"
        elif pe_ratio < 0:
            risk_factors.append("亏损状态")
            risk_level = "较高"
        
        # 技术面风险
        if stock['technical_score'] < 40:
            risk_factors.append("技术面偏弱")
            risk_level = "较高"
        
        # 基本面风险
        if stock['fundamental_score'] < 40:
            risk_factors.append("基本面偏弱")
            risk_level = "较高"
        
        # 市值风险
        market_cap = stock.get('market_cap', 0)
        if market_cap and market_cap < 500000:  # 50亿以下
            risk_factors.append("小市值股票")
        
        return {
            'risk_level': risk_level,
            'risk_factors': risk_factors,
            'suggested_position': self._suggest_position_size(risk_level)
        }
    
    def _suggest_position_size(self, risk_level: str) -> str:
        """建议仓位大小"""
        position_map = {
            "较低": "可适当加大仓位（5-8%）",
            "中等": "标准仓位（3-5%）",
            "较高": "谨慎小仓位（1-3%）"
        }
        return position_map.get(risk_level, "标准仓位（3-5%）")
