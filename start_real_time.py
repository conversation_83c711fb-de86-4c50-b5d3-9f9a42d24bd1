#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时荐股小程序启动脚本
"""

import os
import sys
import subprocess
import time

def check_and_install_packages():
    """检查并安装必要的包"""
    required_packages = [
        'flask',
        'requests', 
        'pandas',
        'python-dotenv'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("📦 正在安装缺少的依赖包...")
        for package in missing_packages:
            try:
                subprocess.check_call([
                    sys.executable, '-m', 'pip', 'install', 
                    '--trusted-host', 'pypi.org',
                    '--trusted-host', 'pypi.python.org', 
                    '--trusted-host', 'files.pythonhosted.org',
                    package
                ])
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"❌ {package} 安装失败")
                return False
    
    return True

def create_env_file():
    """创建环境变量文件"""
    env_file = '.env'
    if not os.path.exists(env_file):
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write("""# Tushare API Token (可选，不配置将使用免费数据源)
TUSHARE_TOKEN=your_tushare_token_here

# Flask配置
SECRET_KEY=real-time-stock-picker-2024
DEBUG=False

# 数据更新间隔（秒）
DATA_UPDATE_INTERVAL=30
""")
        print("✅ 已创建 .env 配置文件")

def main():
    """主函数"""
    print("🚀 启动实时荐股小程序...")
    print("=" * 60)
    
    # 检查并安装依赖
    print("📦 检查依赖包...")
    if not check_and_install_packages():
        print("❌ 依赖包安装失败，请手动安装")
        return
    
    # 创建环境变量文件
    create_env_file()
    
    # 启动应用
    print("🌐 启动实时数据服务...")
    print("=" * 60)
    
    try:
        # 导入并启动应用
        from real_time_app import app, recommendation_engine
        
        # 启动推荐引擎
        recommendation_engine.start()
        
        print("✅ 实时荐股小程序启动成功!")
        print("🌐 访问地址: http://localhost:5000")
        print("📊 正在获取实时数据...")
        print("🔄 数据每30秒自动更新")
        print("\n💡 功能特色:")
        print("   • 实时市场行情")
        print("   • 智能股票推荐") 
        print("   • 热门板块分析")
        print("   • 最新市场资讯")
        print("\n⚠️  注意事项:")
        print("   • 如需更准确数据，请配置Tushare Token")
        print("   • 当前使用免费数据源，可能有延迟")
        print("   • 所有推荐仅供参考，投资需谨慎")
        print("\n按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 启动Flask应用
        app.run(debug=False, host='0.0.0.0', port=5000, threaded=True)
        
    except KeyboardInterrupt:
        print("\n👋 正在停止服务...")
        try:
            recommendation_engine.stop()
        except:
            pass
        print("✅ 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        print("\n🔧 故障排除:")
        print("   1. 检查端口5000是否被占用")
        print("   2. 检查网络连接是否正常")
        print("   3. 尝试重新运行程序")

if __name__ == '__main__':
    main()
