<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能荐股小程序 - 演示版</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header { 
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); 
            color: white; 
            padding: 30px; 
            text-align: center;
        }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; }
        .header p { font-size: 1.1rem; opacity: 0.9; }
        .content { padding: 30px; }
        .section { margin-bottom: 40px; }
        .section-title { 
            font-size: 1.8rem; 
            color: #333; 
            margin-bottom: 20px; 
            padding-bottom: 10px; 
            border-bottom: 3px solid #007bff;
            display: flex;
            align-items: center;
        }
        .section-title i { margin-right: 10px; color: #007bff; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .grid-2 { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }
        .card { 
            background: white; 
            border-radius: 10px; 
            padding: 20px; 
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 4px solid #007bff;
        }
        .card:hover { 
            transform: translateY(-5px); 
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .market-card { 
            text-align: center; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            border-left: none;
        }
        .market-card.positive { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
        .market-card.negative { background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%); }
        .market-price { font-size: 2rem; font-weight: bold; margin: 10px 0; }
        .market-change { font-size: 1.1rem; }
        .sector-card { border-left-color: #28a745; }
        .stock-card { border-left-color: #ffc107; }
        .news-card { border-left-color: #17a2b8; }
        .score-badge { 
            background: #007bff; 
            color: white; 
            padding: 4px 12px; 
            border-radius: 20px; 
            font-size: 0.9rem; 
            font-weight: bold;
            display: inline-block;
        }
        .score-excellent { background: #28a745; }
        .score-good { background: #17a2b8; }
        .score-fair { background: #ffc107; }
        .positive { color: #28a745; font-weight: bold; }
        .negative { color: #dc3545; font-weight: bold; }
        .card-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 15px;
        }
        .card-title { font-size: 1.3rem; font-weight: bold; color: #333; }
        .card-subtitle { color: #666; font-size: 0.9rem; margin-top: 5px; }
        .indicator-row { 
            display: flex; 
            justify-content: space-between; 
            margin: 8px 0; 
            padding: 5px 0;
        }
        .indicator-label { color: #666; }
        .indicator-value { font-weight: bold; }
        .reason { 
            margin-top: 15px; 
            padding: 10px; 
            background: #f8f9fa; 
            border-radius: 5px; 
            color: #666; 
            font-size: 0.9rem;
            border-left: 3px solid #007bff;
        }
        .footer { 
            background: #f8f9fa; 
            padding: 20px; 
            text-align: center; 
            color: #666;
            border-top: 1px solid #eee;
        }
        .warning { 
            background: #fff3cd; 
            border: 1px solid #ffeaa7; 
            border-radius: 8px; 
            padding: 15px; 
            margin: 20px 0;
            color: #856404;
        }
        .warning strong { color: #d63031; }
        @media (max-width: 768px) {
            .container { margin: 10px; border-radius: 10px; }
            .content { padding: 20px; }
            .header { padding: 20px; }
            .header h1 { font-size: 2rem; }
            .grid, .grid-2 { grid-template-columns: 1fr; }
        }
        .animate { animation: fadeInUp 0.6s ease-out; }
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-chart-line"></i> 智能荐股小程序</h1>
            <p>基于Tushare数据的股票推荐系统 - 演示版</p>
        </div>
        
        <div class="content">
            <!-- 市场概览 -->
            <div class="section animate">
                <h2 class="section-title">
                    <i class="fas fa-globe"></i>市场概览
                </h2>
                <div class="grid">
                    <div class="card market-card positive">
                        <h3>上证指数</h3>
                        <div class="market-price">3,245.67</div>
                        <div class="market-change">
                            <i class="fas fa-arrow-up"></i> +15.23 (+0.47%)
                        </div>
                        <div style="margin-top: 10px; font-size: 0.9rem; opacity: 0.9;">
                            成交量: 2,456亿
                        </div>
                    </div>
                    <div class="card market-card positive">
                        <h3>深证成指</h3>
                        <div class="market-price">12,456.89</div>
                        <div class="market-change">
                            <i class="fas fa-arrow-up"></i> +89.45 (+0.72%)
                        </div>
                        <div style="margin-top: 10px; font-size: 0.9rem; opacity: 0.9;">
                            成交量: 3,128亿
                        </div>
                    </div>
                    <div class="card market-card negative">
                        <h3>创业板指</h3>
                        <div class="market-price">2,789.34</div>
                        <div class="market-change">
                            <i class="fas fa-arrow-down"></i> -12.56 (-0.45%)
                        </div>
                        <div style="margin-top: 10px; font-size: 0.9rem; opacity: 0.9;">
                            成交量: 1,892亿
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 板块推荐 -->
            <div class="section animate">
                <h2 class="section-title">
                    <i class="fas fa-fire"></i>热门板块推荐
                </h2>
                <div class="grid-2">
                    <div class="card sector-card">
                        <div class="card-header">
                            <div>
                                <div class="card-title">人工智能</div>
                                <div class="card-subtitle">成分股: 45只</div>
                            </div>
                            <span class="score-badge score-excellent">85分</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">平均涨幅</span>
                            <span class="indicator-value positive">****%</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">技术评分</span>
                            <span class="indicator-value">82.5分</span>
                        </div>
                        <div class="reason">
                            <i class="fas fa-lightbulb"></i> 政策利好，技术突破，相关概念股表现强势，资金持续流入
                        </div>
                    </div>
                    
                    <div class="card sector-card">
                        <div class="card-header">
                            <div>
                                <div class="card-title">新能源汽车</div>
                                <div class="card-subtitle">成分股: 38只</div>
                            </div>
                            <span class="score-badge score-excellent">82分</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">平均涨幅</span>
                            <span class="indicator-value positive">****%</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">技术评分</span>
                            <span class="indicator-value">79.3分</span>
                        </div>
                        <div class="reason">
                            <i class="fas fa-lightbulb"></i> 销量增长，产业链完善，龙头企业受益，市场前景广阔
                        </div>
                    </div>
                    
                    <div class="card sector-card">
                        <div class="card-header">
                            <div>
                                <div class="card-title">医疗器械</div>
                                <div class="card-subtitle">成分股: 52只</div>
                            </div>
                            <span class="score-badge score-good">78分</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">平均涨幅</span>
                            <span class="indicator-value positive">+3.5%</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">技术评分</span>
                            <span class="indicator-value">76.8分</span>
                        </div>
                        <div class="reason">
                            <i class="fas fa-lightbulb"></i> 需求稳定，创新驱动，行业景气度高，政策支持力度大
                        </div>
                    </div>
                    
                    <div class="card sector-card">
                        <div class="card-header">
                            <div>
                                <div class="card-title">半导体</div>
                                <div class="card-subtitle">成分股: 67只</div>
                            </div>
                            <span class="score-badge score-good">75分</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">平均涨幅</span>
                            <span class="indicator-value positive">****%</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">技术评分</span>
                            <span class="indicator-value">73.2分</span>
                        </div>
                        <div class="reason">
                            <i class="fas fa-lightbulb"></i> 国产替代，技术升级，政策支持力度大，长期发展前景良好
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 个股推荐 -->
            <div class="section animate">
                <h2 class="section-title">
                    <i class="fas fa-star"></i>精选个股推荐
                </h2>
                <div class="grid-2">
                    <div class="card stock-card">
                        <div class="card-header">
                            <div>
                                <div class="card-title">比亚迪</div>
                                <div class="card-subtitle">002594.SZ | 汽车制造</div>
                            </div>
                            <span class="score-badge score-excellent">88分</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">当前价格</span>
                            <span class="indicator-value">¥245.80</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">涨跌幅</span>
                            <span class="indicator-value positive">****%</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">市盈率</span>
                            <span class="indicator-value">28.5</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">风险等级</span>
                            <span class="indicator-value" style="color: #28a745;">中等</span>
                        </div>
                        <div class="reason">
                            <i class="fas fa-lightbulb"></i> 新能源汽车龙头，技术领先，市场份额稳定，基本面良好
                        </div>
                    </div>
                    
                    <div class="card stock-card">
                        <div class="card-header">
                            <div>
                                <div class="card-title">宁德时代</div>
                                <div class="card-subtitle">300750.SZ | 电池制造</div>
                            </div>
                            <span class="score-badge score-excellent">85分</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">当前价格</span>
                            <span class="indicator-value">¥198.50</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">涨跌幅</span>
                            <span class="indicator-value positive">****%</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">市盈率</span>
                            <span class="indicator-value">32.1</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">风险等级</span>
                            <span class="indicator-value" style="color: #28a745;">中等</span>
                        </div>
                        <div class="reason">
                            <i class="fas fa-lightbulb"></i> 动力电池龙头，全球市场份额领先，技术优势明显
                        </div>
                    </div>
                    
                    <div class="card stock-card">
                        <div class="card-header">
                            <div>
                                <div class="card-title">贵州茅台</div>
                                <div class="card-subtitle">600519.SH | 白酒制造</div>
                            </div>
                            <span class="score-badge score-good">82分</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">当前价格</span>
                            <span class="indicator-value">¥1,680.00</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">涨跌幅</span>
                            <span class="indicator-value positive">+1.5%</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">市盈率</span>
                            <span class="indicator-value">25.8</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">风险等级</span>
                            <span class="indicator-value" style="color: #ffc107;">较低</span>
                        </div>
                        <div class="reason">
                            <i class="fas fa-lightbulb"></i> 白酒龙头，品牌价值突出，盈利能力强，现金流稳定
                        </div>
                    </div>
                    
                    <div class="card stock-card">
                        <div class="card-header">
                            <div>
                                <div class="card-title">招商银行</div>
                                <div class="card-subtitle">600036.SH | 银行</div>
                            </div>
                            <span class="score-badge score-good">78分</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">当前价格</span>
                            <span class="indicator-value">¥42.80</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">涨跌幅</span>
                            <span class="indicator-value positive">+1.8%</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">市盈率</span>
                            <span class="indicator-value">8.9</span>
                        </div>
                        <div class="indicator-row">
                            <span class="indicator-label">风险等级</span>
                            <span class="indicator-value" style="color: #ffc107;">较低</span>
                        </div>
                        <div class="reason">
                            <i class="fas fa-lightbulb"></i> 零售银行龙头，资产质量优秀，ROE稳定，估值合理
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 市场资讯 -->
            <div class="section animate">
                <h2 class="section-title">
                    <i class="fas fa-newspaper"></i>市场资讯
                </h2>
                <div class="grid">
                    <div class="card news-card">
                        <h4>A股三大指数集体收涨，新能源板块表现强势</h4>
                        <p style="color: #666; margin: 10px 0;">今日A股市场整体表现良好，三大指数均收涨。新能源汽车、光伏等板块涨幅居前，市场情绪回暖，成交量较昨日有所放大...</p>
                        <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.9rem; color: #999;">
                            <span><i class="fas fa-clock"></i> 2024-01-15 15:30</span>
                            <span><i class="fas fa-tag"></i> 财经新闻</span>
                        </div>
                    </div>
                    
                    <div class="card news-card">
                        <h4>央行降准释放流动性，市场情绪回暖</h4>
                        <p style="color: #666; margin: 10px 0;">央行宣布降准0.5个百分点，释放长期资金约1万亿元，有助于维护市场流动性合理充裕，支持实体经济发展...</p>
                        <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.9rem; color: #999;">
                            <span><i class="fas fa-clock"></i> 2024-01-15 14:20</span>
                            <span><i class="fas fa-tag"></i> 金融时报</span>
                        </div>
                    </div>
                    
                    <div class="card news-card">
                        <h4>科技股领涨，人工智能概念持续活跃</h4>
                        <p style="color: #666; margin: 10px 0;">人工智能相关概念股今日表现活跃，多只个股涨停。机构认为AI技术应用前景广阔，相关产业链有望持续受益...</p>
                        <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.9rem; color: #999;">
                            <span><i class="fas fa-clock"></i> 2024-01-15 13:15</span>
                            <span><i class="fas fa-tag"></i> 证券日报</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 风险提示 -->
            <div class="warning">
                <h4><i class="fas fa-exclamation-triangle"></i> <strong>重要风险提示</strong></h4>
                <p style="margin: 10px 0;">
                    • 本系统仅供学习和研究使用，所有推荐和分析仅供参考，不构成投资建议<br>
                    • 股市有风险，投资需谨慎，请根据自身风险承受能力做出投资决策<br>
                    • 历史业绩不代表未来表现，投资者应当具备相应的投资知识和风险识别能力<br>
                    • 建议在专业投资顾问指导下进行投资决策
                </p>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>© 2024 智能荐股小程序</strong></p>
            <p>基于Tushare数据 | 演示版本 | 仅供学习研究使用</p>
        </div>
    </div>
    
    <script>
        // 添加动画效果
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.section');
            sections.forEach((section, index) => {
                setTimeout(() => {
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
        
        // 模拟实时数据更新
        function updateMarketData() {
            const marketCards = document.querySelectorAll('.market-card');
            marketCards.forEach(card => {
                const priceElement = card.querySelector('.market-price');
                const changeElement = card.querySelector('.market-change');
                
                // 模拟价格波动
                const currentPrice = parseFloat(priceElement.textContent.replace(',', ''));
                const change = (Math.random() - 0.5) * 20;
                const newPrice = currentPrice + change;
                const changePercent = (change / currentPrice * 100).toFixed(2);
                
                priceElement.textContent = newPrice.toLocaleString('zh-CN', {minimumFractionDigits: 2});
                
                if (change >= 0) {
                    changeElement.innerHTML = `<i class="fas fa-arrow-up"></i> +${change.toFixed(2)} (+${changePercent}%)`;
                    card.className = 'card market-card positive';
                } else {
                    changeElement.innerHTML = `<i class="fas fa-arrow-down"></i> ${change.toFixed(2)} (${changePercent}%)`;
                    card.className = 'card market-card negative';
                }
            });
        }
        
        // 每30秒更新一次数据（演示用）
        setInterval(updateMarketData, 30000);
    </script>
</body>
</html>
