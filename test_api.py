#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API数据
"""

import requests
import json

try:
    print("正在测试API...")
    r = requests.get('http://localhost:8080/api/data', timeout=10)
    data = r.json()
    
    print("API状态:", data['status'])
    print("更新时间:", data['data']['last_update'])
    
    market = data['data']['market']
    print("\n当前市场数据:")
    for code, info in market.items():
        print(f"{info['name']}: {info['close']:.2f}点 ({info['change_pct']:+.2f}%)")
    
    print(f"\n数据条目数: {len(market)}")
    
except Exception as e:
    print(f"测试失败: {e}")
