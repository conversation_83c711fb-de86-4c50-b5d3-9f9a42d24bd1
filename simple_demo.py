#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版智能荐股演示
"""

import json
import random
from datetime import datetime, timedelta
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse

class SimpleStockHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.serve_html()
        elif self.path.startswith('/api/'):
            self.serve_api()
        else:
            self.send_error(404)
    
    def serve_html(self):
        html = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能荐股小程序</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #007bff; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .section { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin: 10px 0; }
        .positive { color: #28a745; }
        .negative { color: #dc3545; }
        .score { background: #007bff; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .loading { text-align: center; padding: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📈 智能荐股小程序</h1>
            <p>基于Tushare数据的股票推荐系统</p>
        </div>
        
        <div class="section">
            <h2>📊 市场概览</h2>
            <div id="market" class="loading">加载中...</div>
        </div>
        
        <div class="section">
            <h2>🔥 热门板块推荐</h2>
            <div id="sectors" class="loading">加载中...</div>
        </div>
        
        <div class="section">
            <h2>⭐ 精选个股推荐</h2>
            <div id="stocks" class="loading">加载中...</div>
        </div>
        
        <div class="section">
            <h2>📰 市场资讯</h2>
            <div id="news" class="loading">加载中...</div>
        </div>
    </div>

    <script>
        async function loadData() {
            try {
                // 市场概览
                const marketRes = await fetch('/api/market');
                const marketData = await marketRes.json();
                document.getElementById('market').innerHTML = renderMarket(marketData.data);
                
                // 板块推荐
                const sectorRes = await fetch('/api/sectors');
                const sectorData = await sectorRes.json();
                document.getElementById('sectors').innerHTML = renderSectors(sectorData.data);
                
                // 个股推荐
                const stockRes = await fetch('/api/stocks');
                const stockData = await stockRes.json();
                document.getElementById('stocks').innerHTML = renderStocks(stockData.data);
                
                // 新闻资讯
                const newsRes = await fetch('/api/news');
                const newsData = await newsRes.json();
                document.getElementById('news').innerHTML = renderNews(newsData.data);
            } catch (error) {
                console.error('加载失败:', error);
            }
        }
        
        function renderMarket(data) {
            return '<div class="grid">' + Object.entries(data).map(([code, info]) => 
                `<div class="card">
                    <h3>${info.name}</h3>
                    <div style="font-size: 24px; font-weight: bold;">${info.price.toFixed(2)}</div>
                    <div class="${info.change >= 0 ? 'positive' : 'negative'}">
                        ${info.change >= 0 ? '+' : ''}${info.change.toFixed(2)} (${info.change_pct.toFixed(2)}%)
                    </div>
                </div>`
            ).join('') + '</div>';
        }
        
        function renderSectors(data) {
            return '<div class="grid">' + data.map((item, i) => 
                `<div class="card">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h3>${item.name}</h3>
                        <span class="score">${item.score}分</span>
                    </div>
                    <div class="${item.change >= 0 ? 'positive' : 'negative'}">
                        涨幅: ${item.change >= 0 ? '+' : ''}${item.change.toFixed(2)}%
                    </div>
                    <div style="margin-top: 10px; color: #666; font-size: 14px;">
                        ${item.reason}
                    </div>
                </div>`
            ).join('') + '</div>';
        }
        
        function renderStocks(data) {
            return '<div class="grid">' + data.map((item, i) => 
                `<div class="card">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <h3>${item.name}</h3>
                            <small>${item.code} | ${item.industry}</small>
                        </div>
                        <span class="score">${item.score}分</span>
                    </div>
                    <div>价格: ¥${item.price.toFixed(2)}</div>
                    <div class="${item.change >= 0 ? 'positive' : 'negative'}">
                        涨跌: ${item.change >= 0 ? '+' : ''}${item.change.toFixed(2)}%
                    </div>
                    <div style="margin-top: 10px; color: #666; font-size: 14px;">
                        ${item.reason}
                    </div>
                </div>`
            ).join('') + '</div>';
        }
        
        function renderNews(data) {
            return data.map(item => 
                `<div class="card">
                    <h4>${item.title}</h4>
                    <p style="color: #666;">${item.content}</p>
                    <small style="color: #999;">${item.time} | ${item.source}</small>
                </div>`
            ).join('');
        }
        
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>'''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def serve_api(self):
        if self.path == '/api/market':
            data = {
                '000001.SH': {'name': '上证指数', 'price': 3200 + random.uniform(-50, 50), 'change': random.uniform(-30, 30), 'change_pct': random.uniform(-2, 2)},
                '399001.SZ': {'name': '深证成指', 'price': 12500 + random.uniform(-200, 200), 'change': random.uniform(-100, 100), 'change_pct': random.uniform(-2, 2)},
                '399006.SZ': {'name': '创业板指', 'price': 2800 + random.uniform(-100, 100), 'change': random.uniform(-50, 50), 'change_pct': random.uniform(-3, 3)}
            }
        elif self.path == '/api/sectors':
            data = [
                {'name': '人工智能', 'score': 85, 'change': 5.2, 'reason': '政策利好，技术突破'},
                {'name': '新能源汽车', 'score': 82, 'change': 4.8, 'reason': '销量增长，产业链完善'},
                {'name': '医疗器械', 'score': 78, 'change': 3.5, 'reason': '需求稳定，创新驱动'},
                {'name': '半导体', 'score': 75, 'change': 2.8, 'reason': '国产替代，技术升级'},
                {'name': '光伏产业', 'score': 73, 'change': 2.2, 'reason': '成本下降，需求旺盛'}
            ]
        elif self.path == '/api/stocks':
            data = [
                {'name': '比亚迪', 'code': '002594.SZ', 'industry': '汽车制造', 'price': 245.80, 'change': 3.2, 'score': 88, 'reason': '新能源汽车龙头，技术领先'},
                {'name': '宁德时代', 'code': '300750.SZ', 'industry': '电池制造', 'price': 198.50, 'change': 2.8, 'score': 85, 'reason': '动力电池龙头，市场份额领先'},
                {'name': '贵州茅台', 'code': '600519.SH', 'industry': '白酒制造', 'price': 1680.00, 'change': 1.5, 'score': 82, 'reason': '白酒龙头，品牌价值突出'},
                {'name': '腾讯控股', 'code': '00700.HK', 'industry': '互联网', 'price': 368.20, 'change': 2.1, 'score': 80, 'reason': '互联网巨头，业务多元化'},
                {'name': '招商银行', 'code': '600036.SH', 'industry': '银行', 'price': 42.80, 'change': 1.8, 'score': 78, 'reason': '零售银行龙头，资产质量优秀'}
            ]
        elif self.path == '/api/news':
            data = [
                {'title': 'A股三大指数集体收涨，新能源板块表现强势', 'content': '今日A股市场整体表现良好，三大指数均收涨。新能源汽车、光伏等板块涨幅居前...', 'time': '2024-01-15 15:30', 'source': '财经新闻'},
                {'title': '央行降准释放流动性，市场情绪回暖', 'content': '央行宣布降准0.5个百分点，释放长期资金约1万亿元，有助于维护市场流动性合理充裕...', 'time': '2024-01-15 14:20', 'source': '金融时报'},
                {'title': '科技股领涨，人工智能概念持续活跃', 'content': '人工智能相关概念股今日表现活跃，多只个股涨停。机构认为AI技术应用前景广阔...', 'time': '2024-01-15 13:15', 'source': '证券日报'}
            ]
        else:
            self.send_error(404)
            return
        
        response = {'status': 'success', 'data': data}
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

if __name__ == '__main__':
    port = 8000
    try:
        server = HTTPServer(('localhost', port), SimpleStockHandler)
        print(f"🚀 智能荐股演示启动成功!")
        print(f"🌐 访问地址: http://localhost:{port}")
        print("📊 使用模拟数据演示功能")
        print("按 Ctrl+C 停止服务")
        print("=" * 50)
        server.serve_forever()
    except KeyboardInterrupt:
        print("\\n👋 服务已停止")
        server.shutdown()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
