#!/usr/bin/env python3
"""
智能荐股小程序启动脚本
"""

import os
import sys
import logging
from datetime import datetime

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'tushare', 'pandas', 'numpy', 'flask', 
        'requests', 'python-dateutil', 'python-dotenv'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def check_config():
    """检查配置"""
    from dotenv import load_dotenv
    load_dotenv()
    
    tushare_token = os.getenv('TUSHARE_TOKEN')
    
    if not tushare_token or tushare_token == 'your_tushare_token_here':
        print("❌ Tushare Token未配置!")
        print("\n请按以下步骤配置:")
        print("1. 访问 https://tushare.pro 注册账户")
        print("2. 获取API Token")
        print("3. 复制 .env.example 为 .env")
        print("4. 在 .env 文件中配置 TUSHARE_TOKEN")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 启动智能荐股小程序...")
    print("=" * 50)
    
    # 检查依赖
    print("📦 检查依赖包...")
    if not check_dependencies():
        sys.exit(1)
    print("✅ 依赖包检查通过")
    
    # 检查配置
    print("⚙️  检查配置...")
    if not check_config():
        sys.exit(1)
    print("✅ 配置检查通过")
    
    # 启动应用
    print("🌐 启动Web服务...")
    print("=" * 50)
    
    try:
        from app import app, logger
        
        logger.info("智能荐股小程序启动成功")
        print("✅ 应用启动成功!")
        print("🌐 访问地址: http://localhost:5000")
        print("📊 开始享受智能荐股服务!")
        print("\n按 Ctrl+C 停止服务")
        print("=" * 50)
        
        app.run(debug=False, host='0.0.0.0', port=5000)
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
