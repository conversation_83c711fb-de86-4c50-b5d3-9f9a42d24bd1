"""
荐股小程序主应用
基于Tushare数据的智能荐股系统
"""

from flask import Flask, render_template, jsonify, request
import logging
from datetime import datetime, timedelta
import json

from config import Config
from data_fetcher import DataFetcher
from analyzer import StockAnalyzer
from recommender import StockRecommender

# 配置日志
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(Config.LOG_FILE),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
app.config.from_object(Config)

# 初始化组件
data_fetcher = DataFetcher()
analyzer = StockAnalyzer()
recommender = StockRecommender()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/recommendations')
def get_recommendations():
    """获取股票推荐"""
    try:
        # 获取推荐类型
        rec_type = request.args.get('type', 'sector')  # sector, stock, all
        
        if rec_type == 'sector':
            recommendations = recommender.get_sector_recommendations()
        elif rec_type == 'stock':
            recommendations = recommender.get_stock_recommendations()
        else:
            recommendations = {
                'sectors': recommender.get_sector_recommendations(),
                'stocks': recommender.get_stock_recommendations()
            }
        
        return jsonify({
            'status': 'success',
            'data': recommendations,
            'timestamp': datetime.now().isoformat()
        })
    
    except Exception as e:
        logger.error(f"获取推荐失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/analysis/<stock_code>')
def get_stock_analysis(stock_code):
    """获取个股分析"""
    try:
        # 获取股票数据
        stock_data = data_fetcher.get_stock_daily(stock_code, days=60)
        
        if stock_data.empty:
            return jsonify({
                'status': 'error',
                'message': '未找到股票数据'
            }), 404
        
        # 进行技术分析
        technical_analysis = analyzer.technical_analysis(stock_data)
        
        # 获取基本面数据
        fundamental_data = data_fetcher.get_stock_basic_info(stock_code)
        financial_data = data_fetcher.get_financial_data(stock_code)
        
        # 基本面分析
        fundamental_analysis = analyzer.fundamental_analysis(
            fundamental_data, financial_data
        )
        
        return jsonify({
            'status': 'success',
            'data': {
                'stock_code': stock_code,
                'technical': technical_analysis,
                'fundamental': fundamental_analysis,
                'price_data': stock_data.tail(30).to_dict('records')
            }
        })
    
    except Exception as e:
        logger.error(f"分析股票 {stock_code} 失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/sectors')
def get_sectors():
    """获取板块信息"""
    try:
        sectors = data_fetcher.get_sector_data()
        return jsonify({
            'status': 'success',
            'data': sectors
        })
    except Exception as e:
        logger.error(f"获取板块信息失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/news')
def get_news():
    """获取相关资讯"""
    try:
        news_data = data_fetcher.get_news_data(limit=20)
        return jsonify({
            'status': 'success',
            'data': news_data
        })
    except Exception as e:
        logger.error(f"获取资讯失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/market_overview')
def get_market_overview():
    """获取市场概览"""
    try:
        # 获取主要指数数据
        indices = ['000001.SH', '399001.SZ', '399006.SZ']  # 上证指数、深证成指、创业板指
        market_data = {}
        
        for index_code in indices:
            index_data = data_fetcher.get_index_daily(index_code, days=5)
            if not index_data.empty:
                latest = index_data.iloc[-1]
                prev = index_data.iloc[-2] if len(index_data) > 1 else latest
                
                market_data[index_code] = {
                    'name': data_fetcher.get_index_name(index_code),
                    'close': float(latest['close']),
                    'change': float(latest['close'] - prev['close']),
                    'change_pct': float((latest['close'] - prev['close']) / prev['close'] * 100),
                    'volume': float(latest['vol'])
                }
        
        return jsonify({
            'status': 'success',
            'data': market_data
        })
    
    except Exception as e:
        logger.error(f"获取市场概览失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

if __name__ == '__main__':
    logger.info("启动荐股小程序...")
    app.run(debug=Config.DEBUG, host='0.0.0.0', port=5000)
