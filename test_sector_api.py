#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_sector_api():
    """测试板块API"""
    print("测试板块API...")
    
    # 测试半导体板块
    concept_code = 'BK0447'
    url = f'https://push2.eastmoney.com/api/qt/stock/get?cb=jQuery&secid=90.{concept_code}&ut=fa5fd1943c7b386f172d6893dbfba10b&fields=f43,f44,f45,f46,f47,f48,f49,f50,f51,f52,f53,f54,f55,f56,f57,f58'
    
    print(f"请求URL: {url}")
    
    try:
        response = requests.get(url, verify=False, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text[:200]}...")
        
        if response.status_code == 200 and response.text:
            # 解析JSONP响应
            json_str = response.text
            if 'jQuery(' in json_str and json_str.endswith(');'):
                json_str = json_str[json_str.find('(')+1:json_str.rfind(')')]
            
            data = json.loads(json_str)
            print(f"解析后数据: {data}")
            
            if 'data' in data and data['data'] and 'f43' in data['data']:
                stock_data = data['data']
                current_price = stock_data['f43'] / 100
                change = stock_data['f44'] / 100
                change_pct = stock_data['f45'] / 100
                
                print(f"半导体板块:")
                print(f"  当前价格: {current_price}")
                print(f"  涨跌额: {change}")
                print(f"  涨跌幅: {change_pct:+.2f}%")
            else:
                print("数据为空或格式不正确")
    
    except Exception as e:
        print(f"错误: {e}")

if __name__ == '__main__':
    test_sector_api()
