<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能荐股小程序 - 基于Tushare数据分析</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>智能荐股系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#market-overview">市场概览</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#sector-recommendations">板块推荐</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#stock-recommendations">个股推荐</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#news-section">市场资讯</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 市场概览 -->
        <section id="market-overview" class="mb-5">
            <div class="row">
                <div class="col-12">
                    <h2 class="section-title">
                        <i class="fas fa-globe me-2"></i>市场概览
                        <button class="btn btn-sm btn-outline-primary ms-2" onclick="refreshMarketData()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </h2>
                </div>
            </div>
            <div class="row" id="market-indices">
                <div class="col-12">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载市场数据...</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 板块推荐 -->
        <section id="sector-recommendations" class="mb-5">
            <div class="row">
                <div class="col-12">
                    <h2 class="section-title">
                        <i class="fas fa-layer-group me-2"></i>热门板块推荐
                        <button class="btn btn-sm btn-outline-success ms-2" onclick="refreshSectorRecommendations()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </h2>
                </div>
            </div>
            <div class="row" id="sector-list">
                <div class="col-12">
                    <div class="text-center">
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">分析中...</span>
                        </div>
                        <p class="mt-2">正在分析板块数据...</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 个股推荐 -->
        <section id="stock-recommendations" class="mb-5">
            <div class="row">
                <div class="col-12">
                    <h2 class="section-title">
                        <i class="fas fa-star me-2"></i>精选个股推荐
                        <button class="btn btn-sm btn-outline-warning ms-2" onclick="refreshStockRecommendations()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </h2>
                </div>
            </div>
            <div class="row" id="stock-list">
                <div class="col-12">
                    <div class="text-center">
                        <div class="spinner-border text-warning" role="status">
                            <span class="visually-hidden">分析中...</span>
                        </div>
                        <p class="mt-2">正在分析个股数据...</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 市场资讯 -->
        <section id="news-section" class="mb-5">
            <div class="row">
                <div class="col-12">
                    <h2 class="section-title">
                        <i class="fas fa-newspaper me-2"></i>市场资讯
                        <button class="btn btn-sm btn-outline-info ms-2" onclick="refreshNews()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </h2>
                </div>
            </div>
            <div class="row" id="news-list">
                <div class="col-12">
                    <div class="text-center">
                        <div class="spinner-border text-info" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载资讯数据...</p>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 股票详情模态框 -->
    <div class="modal fade" id="stockDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">股票详细分析</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="stockDetailContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6>免责声明</h6>
                    <p class="small text-muted">
                        本系统提供的股票推荐仅供参考，不构成投资建议。
                        投资有风险，入市需谨慎。请根据自身风险承受能力做出投资决策。
                    </p>
                </div>
                <div class="col-md-6">
                    <h6>数据来源</h6>
                    <p class="small text-muted">
                        数据来源：Tushare Pro<br>
                        更新时间：<span id="last-update-time">--</span>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
