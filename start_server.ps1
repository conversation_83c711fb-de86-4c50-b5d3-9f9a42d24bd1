# PowerShell脚本启动股票推荐服务器

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "股票推荐服务器启动脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 获取脚本所在目录
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptDir

Write-Host "当前目录: $scriptDir" -ForegroundColor Green
Write-Host ""

# 检查Python是否可用
Write-Host "检查Python环境..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Python已找到: $pythonVersion" -ForegroundColor Green
        $pythonCmd = "python"
    } else {
        throw "Python命令失败"
    }
} catch {
    try {
        $pythonVersion = py --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Python已找到 (使用py命令): $pythonVersion" -ForegroundColor Green
            $pythonCmd = "py"
        } else {
            throw "py命令也失败"
        }
    } catch {
        Write-Host "❌ Python未找到或未正确安装" -ForegroundColor Red
        Write-Host "请安装Python并确保已添加到系统PATH" -ForegroundColor Red
        Read-Host "按Enter键退出"
        exit 1
    }
}

# 检查端口是否被占用
Write-Host "检查端口8888..." -ForegroundColor Yellow
$portCheck = netstat -ano | Select-String ":8888"
if ($portCheck) {
    Write-Host "⚠️ 端口8888已被占用，尝试终止占用进程..." -ForegroundColor Yellow
    $processes = netstat -ano | Select-String ":8888" | ForEach-Object { ($_ -split '\s+')[-1] } | Sort-Object -Unique
    foreach ($pid in $processes) {
        try {
            Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
            Write-Host "已终止进程 PID: $pid" -ForegroundColor Green
        } catch {
            Write-Host "无法终止进程 PID: $pid" -ForegroundColor Yellow
        }
    }
    Start-Sleep -Seconds 2
} else {
    Write-Host "✅ 端口8888可用" -ForegroundColor Green
}

# 检查HTML文件是否存在
$htmlFile = Join-Path $scriptDir "final_accurate_stock_app.html"
if (Test-Path $htmlFile) {
    Write-Host "✅ 找到HTML文件: final_accurate_stock_app.html" -ForegroundColor Green
} else {
    Write-Host "❌ 未找到HTML文件: final_accurate_stock_app.html" -ForegroundColor Red
    Write-Host "请确保文件存在于当前目录" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

Write-Host ""
Write-Host "启动HTTP服务器..." -ForegroundColor Yellow
Write-Host "命令: $pythonCmd -m http.server 8888" -ForegroundColor Gray
Write-Host ""

# 启动服务器
try {
    Start-Process -FilePath $pythonCmd -ArgumentList "-m", "http.server", "8888" -WorkingDirectory $scriptDir -WindowStyle Normal
    
    Write-Host "✅ 服务器启动成功!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 访问地址:" -ForegroundColor Cyan
    Write-Host "   http://localhost:8888/final_accurate_stock_app.html" -ForegroundColor White
    Write-Host ""
    Write-Host "📊 数据确认:" -ForegroundColor Cyan
    Write-Host "   ✅ 半导体板块: 指数2831.81, 涨跌幅-1.15%" -ForegroundColor White
    Write-Host "   ✅ 智能板块推荐显示完整数据" -ForegroundColor White
    Write-Host ""
    
    # 等待服务器启动
    Start-Sleep -Seconds 3
    
    # 打开浏览器
    Write-Host "正在打开浏览器..." -ForegroundColor Yellow
    Start-Process "http://localhost:8888/final_accurate_stock_app.html"
    
    Write-Host ""
    Write-Host "🎯 服务器已启动并在浏览器中打开!" -ForegroundColor Green
    Write-Host "按Ctrl+C停止服务器" -ForegroundColor Yellow
    
} catch {
    Write-Host "❌ 启动服务器失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 备用方案:" -ForegroundColor Yellow
    Write-Host "请手动双击 final_accurate_stock_app.html 文件" -ForegroundColor White
}

Read-Host "按Enter键退出"
